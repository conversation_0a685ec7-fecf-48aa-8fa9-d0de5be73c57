package com.concise.gen.correctionobjectinformation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.param.CorrectionObjectInformationParam;

import java.util.List;
import java.util.Set;

/**
 * 矫正对象信息表-service接口
 *
 * <AUTHOR>
 * @date 2021-12-07 14:19:57
 */
public interface CorrectionObjectInformationService extends IService<CorrectionObjectInformation> {

    /**
     * 查询矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    PageResult<CorrectionObjectInformation> page(CorrectionObjectInformationParam correctionObjectInformationParam, Set<String> org);

    /**
     * 矫正对象信息表-列表
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    List<CorrectionObjectInformation> list(CorrectionObjectInformationParam correctionObjectInformationParam, Set<String> org);

    /**
     * 添加矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    void add(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 删除矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    void delete(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 编辑矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    void edit(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 查看矫正对象信息表-
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    CorrectionObjectInformation detail(CorrectionObjectInformationParam correctionObjectInformationParam);

}
