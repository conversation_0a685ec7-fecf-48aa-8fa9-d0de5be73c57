package com.concise.gen.correctionobjectinformation.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.List;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 矫正对象信息表-吴兴
 *
 * <AUTHOR>
 * @date 2021-12-07 14:19:57
 */
@Data
@TableName("correction_object_information")
public class CorrectionObjectInformation{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 社区矫正人员编号
     */
    private String sqjzrybh;

    /**
     * 是否调查评估
     */
    private String sfdcpg;

    /**
     * 矫正类别
     */
    private String jzlb;

    /**
     * 矫正类别中文值
     */
    private String jzlbName;

    /**
     * 是否成年
     */
    private String sfcn;

    /**
     * 是否成年中文值
     */
    private String sfcnName;

    /**
     * 未成年
     */
    private String wcn;

    /**
     * 未成年中文值
     */
    private String wcnName;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 曾用名
     */
    private String cym;

    /**
     *  性别
     */
    private String xb;

    /**
     * 性别中文值
     */
    private String xbName;

    /**
     *  民族
     */
    private String mz;

    /**
     * 民族中文值
     */
    private String mzName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date csrq;

    /**
     * 有无港澳台身份证
     */
    private String ywgatsfz;

    /**
     * 港澳台身份证类型
     */
    private String gatsfzlx;

    /**
     * 港澳台身份证类型中文值
     */
    private String gatsfzlxName;

    /**
     * 港澳台身份证号码
     */
    private String gatsfzhm;

    /**
     * 有无护照
     */
    private String ywhz;

    /**
     * 护照号码
     */
    private String hzhm;

    /**
     * 护照保存状态
     */
    private String hzbczt;

    /**
     * 护照保存状态中文值
     */
    private String hzbcztName;

    /**
     * 有无港澳台通行证
     */
    private String ywgattxz;

    /**
     * 港澳台通行证类型
     */
    private String gattxzlx;

    /**
     * 港澳台通行证类型中文值
     */
    private String gattxzlxName;

    /**
     * 港澳台通行证号码
     */
    private String gattxzhm;

    /**
     * 港澳台通行证保存状态
     */
    private String gattxzbczt;

    /**
     * 港澳台通行证保存状态中文值
     */
    private String gattxzbcztName;

    /**
     * 有无港澳居民往来内地通行证
     */
    private String ywgajmwlndtxz;

    /**
     * 港澳居往来内地通行证号码
     */
    private String gajmwlndtxz;

    /**
     * 港澳居民往来内地通行证保存状态
     */
    private String gajmwlndtxzbczt;

    /**
     * 港澳居民往来内地通行证保存状态中文值
     */
    private String gajmwlndtxzbcztName;

    /**
     * 有无台胞证
     */
    private String ywtbz;

    /**
     * 台胞证号码
     */
    private String tbzhm;

    /**
     * 台胞证保存状态
     */
    private String tbzbczt;

    /**
     * 台胞证保存状态中文值
     */
    private String tbzbcztName;

    /**
     * 暂予监外执行人员身体状况
     */
    private String zyjwzxrystzk;

    /**
     * 暂予监外执行人员身体状况中文值
     */
    private String zyjwzxrystzkName;

    /**
     * 最后就诊医院
     */
    private String zhjzyy;

    /**
     * 是否有精神病
     */
    private String sfyjsb;

    /**
     * 鉴定机构
     */
    private String jdjg;

    /**
     * 是否有传染病
     */
    private String sfycrb;

    /**
     * 具体传染病
     */
    private String jtcrb;

    /**
     * 具体传染病中文值
     */
    private String jtcrbName;

    /**
     * 文化程度
     */
    private String whcd;

    /**
     * 文化程度中文值
     */
    private String whcdName;

    /**
     * 婚姻状况
     */
    private String hyzk;

    /**
     * 婚姻状况中文值
     */
    private String hyzkName;

    /**
     * 捕前职业
     */
    private String pqzy;

    /**
     * 捕前职业中文值
     */
    private String pqzyName;

    /**
     * 就业就学情况
     */
    private String jyjxqk;

    /**
     * 就业就学情况中文值
     */
    private String jyjxqkName;

    /**
     * 现政治面貌
     */
    private String xzzmn;

    /**
     * 现政治面貌中文值
     */
    private String xzzmnName;

    /**
     * 原政治面貌
     */
    private String yzzmm;

    /**
     * 原政治面貌中文值
     */
    private String yzzmmName;

    /**
     * 原工作单位
     */
    private String ygzdw;

    /**
     * 现工作单位中文值
     */
    private String xgzdwName;

    /**
     * 单位联系电话
     */
    private String dwlxdh;

    /**
     * 个人联系电话
     */
    private String grlxdh;

    /**
     * 国籍
     */
    private String gj;

    /**
     * 国籍中文值
     */
    private String gjName;

    /**
     * 有无家庭成员及主要社会关系
     */
    private String yxjtcyjzyshgx;

    /**
     * 户籍地是否与居住地相同
     */
    private String hjdsfyjzdxt;

    /**
     * 固定居住地所在省（区、市）
     */
    private String gdjzdszs;

    /**
     * 固定居住地所在省（区、市）中文值
     */
    private String gdjzdszsName;

    /**
     * 固定居住地所在地（市、州）
     */
    private String gdjzdszds;

    /**
     * 固定居住地所在地（市、州）中文值
     */
    private String gdjzdszdsName;

    /**
     * 固定居住地所在县（市、区）
     */
    private String gdjzdszxq;

    /**
     * 固定居住地所在县（市、区）中文值
     */
    private String gdjzdszxqName;

    /**
     * 固定居住地（乡镇、街道）
     */
    private String gdjzd;

    /**
     * 固定居住地（乡镇、街道）中文值
     */
    private String gdjzdName;

    /**
     * 固定居住地明细
     */
    private String gdjzdmx;

    /**
     * 户籍所在省（区、市）
     */
    private String hjszs;

    /**
     * 户籍所在省（区、市）中文值
     */
    private String hjszsName;

    /**
     * 户籍所在地（市、州）
     */
    private String hjszds;

    /**
     * 户籍所在地（市、州）中文值
     */
    private String hjszdsName;

    /**
     * 户籍所在县（市、区）
     */
    private String hjszxq;

    /**
     * 户籍所在县（市、区）中文值
     */
    private String hjszxqName;

    /**
     * 户籍所在地（乡镇、街道）
     */
    private String hjszd;

    /**
     * 户籍所在地（乡镇、街道）中文值
     */
    private String hjszdName;

    /**
     * 户籍所在地明细
     */
    private String hjszdmx;

    /**
     * 是否三无人员
     */
    private String sfswry;

    /**
     * 矫正机构
     */
    private String jzjg;

    /**
     * 矫正机构中文值
     */
    private String jzjgName;

    /**
     * 是否有前科
     */
    private String sfyqk;

    /**
     * 是否累犯
     */
    private String sflf;

    /**
     * 前科类型
     */
    private String qklx;

    /**
     * 前科类型中文值
     */
    private String qklxName;

    /**
     * 主要犯罪事实
     */
    private String zyfzss;

    /**
     * 社区矫正期限
     */
    private String sqjzqx;

    /**
     * 社区矫正开始日期
     */
    @Excel(name = "社区矫正开始日期", databaseFormat = "yyyy-MM-dd", format = "yyyy-MM-dd", width = 20)
    private Date sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    @Excel(name = "社区矫正结束日期", databaseFormat = "yyyy-MM-dd", format = "yyyy-MM-dd", width = 20)
    private Date sqjzjsrq;

    /**
     * 犯罪类型
     */
    private String fzlx;

    /**
     * 犯罪类型中文值
     */
    private String fzlxName;

    /**
     * 具体罪名
     */
    private String jtzm;

    /**
     * 具体罪名中文值
     */
    private String jtzmName;

    /**
     * 是否“五独”
     */
    private String sfwd;

    /**
     * 是否“五涉”
     */
    private String sfws;

    /**
     * 是否有“四史”
     */
    private String sfyss;

    /**
     * 是否被宣告禁止令
     */
    private String sfbxgjzl;

    /**
     * 社区矫正人员接收日期
     */
    @Excel(name = "社区矫正人员接收日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date sqjzryjsrq;

    /**
     * 社区矫正人员接收方式
     */
    private String sqjzryjsfs;

    /**
     * 社区矫正人员接收方式中文
     */
    private String sqjzryjsfsName;

    /**
     * 报到情况
     */
    private String bdqk;

    /**
     * 报到情况中文值
     */
    private String bdqkName;

    /**
     * 未按时报到情况说明
     */
    private String wasbdqksm;

    /**
     * 是否建立矫正小组
     */
    private String sfjljzxz;

    /**
     * 是否采用电子定位管理
     */
    private String sfcydzdwgl;

    /**
     * 电子定位方式
     */
    private String dzdwfs;

    /**
     * 电子定位方式中文值
     */
    private String dzdwfsName;

    /**
     * 定位号码
     */
    private String dwhm;

    /**
     * 备注
     */
    private String bz;

    /**
     * 状态
     */
    private String zhuangtai;

    /**
     * 入矫日期
     */
    private Date rujiaoriqi;

    /**
     * 最后修改时间
     */
    private Date lastModifiedTime;

    /**
     * 处理等级
     */
    private String jzjb;
    /**
     * 处理等级名称
     */
    private String jzjbName;

    /**
     * 入矫日期_begin
     */
    @TableField(exist = false)
    private String rujiaoriqi_begin;

    /**
     * 入矫日期_end
     */
    @TableField(exist = false)
    private String rujiaoriqi_end;

    /**
     * 原判刑期开始时间
     */
    private String ypxqkssj;
}
