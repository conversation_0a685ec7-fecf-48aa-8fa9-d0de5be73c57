package com.concise.gen.correctionobjectinformation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.correctionobjectinformation.enums.CorrectionObjectInformationExceptionEnum;
import com.concise.gen.correctionobjectinformation.mapper.CorrectionObjectInformationMapper;
import com.concise.gen.correctionobjectinformation.param.CorrectionObjectInformationParam;
import com.concise.gen.correctionobjectinformation.service.CorrectionObjectInformationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;

/**
 * 矫正对象信息表-service接口实现类
 *
 * <AUTHOR>
 * @date 2021-12-07 14:19:57
 */
@Service
public class CorrectionObjectInformationServiceImpl extends ServiceImpl<CorrectionObjectInformationMapper, CorrectionObjectInformation> implements CorrectionObjectInformationService {



    @Override
    public PageResult<CorrectionObjectInformation> page(CorrectionObjectInformationParam correctionObjectInformationParam, Set<String> org) {
        QueryWrapper<CorrectionObjectInformation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionObjectInformationParam)) {

            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionObjectInformation::getJzjg, org);

            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJzlb())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getJzlb, correctionObjectInformationParam.getJzlb());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXm())) {
                queryWrapper.lambda().like(CorrectionObjectInformation::getXm, correctionObjectInformationParam.getXm());
            }
            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getZhuangtai())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getZhuangtai, correctionObjectInformationParam.getZhuangtai());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getRujiaoriqi_begin())) {
                queryWrapper.lambda().ge(CorrectionObjectInformation::getRujiaoriqi, correctionObjectInformationParam.getRujiaoriqi_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getRujiaoriqi_end())) {
                queryWrapper.lambda().le(CorrectionObjectInformation::getRujiaoriqi, correctionObjectInformationParam.getRujiaoriqi_end());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzryIds())) {
                queryWrapper.lambda().in(CorrectionObjectInformation::getId, correctionObjectInformationParam.getSqjzryIds());
            }
            queryWrapper.orderByDesc("rujiaoriqi");

            if (0 == correctionObjectInformationParam.getTag()) {
                // 查在矫
                queryWrapper.lambda().eq(CorrectionObjectInformation::getZhuangtai, "200");
            } else {
                // 查在矫以外的
                queryWrapper.lambda().ne(CorrectionObjectInformation::getZhuangtai, "200");
            }

        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionObjectInformation> list(CorrectionObjectInformationParam correctionObjectInformationParam, Set<String> org) {
        QueryWrapper<CorrectionObjectInformation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionObjectInformationParam)) {

            // 根据矫正机构id 查询
            queryWrapper.lambda().in(CorrectionObjectInformation::getJzjg, org);

            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJzlb())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getJzlb, correctionObjectInformationParam.getJzlb());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXm())) {
                queryWrapper.lambda().like(CorrectionObjectInformation::getXm, correctionObjectInformationParam.getXm());
            }
            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getZhuangtai())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getZhuangtai, correctionObjectInformationParam.getZhuangtai());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getRujiaoriqi_begin())) {
                queryWrapper.lambda().ge(CorrectionObjectInformation::getRujiaoriqi, correctionObjectInformationParam.getRujiaoriqi_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getRujiaoriqi_end())) {
                queryWrapper.lambda().le(CorrectionObjectInformation::getRujiaoriqi, correctionObjectInformationParam.getRujiaoriqi_end());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzryIds())) {
                queryWrapper.lambda().in(CorrectionObjectInformation::getId, correctionObjectInformationParam.getSqjzryIds());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getLastModifiedTime())) {
                queryWrapper.lambda().ge(CorrectionObjectInformation::getLastModifiedTime, correctionObjectInformationParam.getLastModifiedTime());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzjsrq_begin())) {
                queryWrapper.lambda().ge(CorrectionObjectInformation::getSqjzjsrq, correctionObjectInformationParam.getSqjzjsrq_begin());
            }
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzjsrq_end())) {
                queryWrapper.lambda().le(CorrectionObjectInformation::getSqjzjsrq, correctionObjectInformationParam.getSqjzjsrq_end());
            }
            queryWrapper.orderByDesc("rujiaoriqi");

            if (0 == correctionObjectInformationParam.getTag()) {
                // 查在矫
                queryWrapper.lambda().ge(CorrectionObjectInformation::getZhuangtai, "200");
            } else {
                // 查在矫以外的
                queryWrapper.lambda().ne(CorrectionObjectInformation::getZhuangtai, "200");
            }
        }
        return this.list(queryWrapper);
    }

    @Override
    public void add(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = new CorrectionObjectInformation();
        BeanUtil.copyProperties(correctionObjectInformationParam, correctionObjectInformation);
        this.save(correctionObjectInformation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionObjectInformationParam correctionObjectInformationParam) {
        this.removeById(correctionObjectInformationParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = this.queryCorrectionObjectInformation(correctionObjectInformationParam);
        BeanUtil.copyProperties(correctionObjectInformationParam, correctionObjectInformation);
        this.updateById(correctionObjectInformation);
    }

    @Override
    public CorrectionObjectInformation detail(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return this.queryCorrectionObjectInformation(correctionObjectInformationParam);
    }

    /**
     * 获取矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-12-07 14:19:57
     */
    private CorrectionObjectInformation queryCorrectionObjectInformation(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = this.getById(correctionObjectInformationParam.getId());
        if (ObjectUtil.isNull(correctionObjectInformation)) {
            throw new ServiceException(CorrectionObjectInformationExceptionEnum.NOT_EXIST);
        }
        return correctionObjectInformation;
    }
}
