package com.concise.common.file.util;

import java.net.URL;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;

import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云OSS对象存储预签名URL工具类
 * 支持使用自定义域名生成预签名URL
 */
@Slf4j
public class OssSignedUrlUtil {

    // 硬编码配置参数
    private static final String endpoint = "oss-cn-hangzhou-zjzwy01-d01-a.cloud-inner.zj.gov.cn";
    private static final String accessKeyId = "6rXVZHvIxgi4VPtt";
    private static final String accessKeySecret = "lgOgEh2gE4KBlZDaYfrVItOWrxibEX";
    private static final String bucketName = "zlpfoss-1";
    private static final String region = "oss-cn-hangzhou-zjzwy01-d01-a";
    private static final String publicDomain = "https://zlpf.zjsft.gov.cn/oss";
    private static final String staticDomain = "https://zlpfoss-1.oss-cn-hangzhou-zjzwy01-d01-a.cloud-inner.zj.gov.cn";
    private static String active = "prod"; // 默认为生产环境

    private static OSS ossClient = null;



    /**
     * 设置OSS服务端点（硬编码模式下忽略）
     *
     * @param endpoint OSS服务端点
     */
    public static void setEndpoint(String endpoint) {
        log.debug("使用硬编码OSS配置，忽略setEndpoint调用: {}", endpoint);
    }

    /**
     * 设置访问密钥ID（硬编码模式下忽略）
     *
     * @param accessKeyId 访问密钥ID
     */
    public static void setAccessKeyId(String accessKeyId) {
        log.debug("使用硬编码OSS配置，忽略setAccessKeyId调用");
    }

    /**
     * 设置访问密钥密码（硬编码模式下忽略）
     *
     * @param accessKeySecret 访问密钥密码
     */
    public static void setAccessKeySecret(String accessKeySecret) {
        log.debug("使用硬编码OSS配置，忽略setAccessKeySecret调用");
    }

    /**
     * 设置存储桶名称（硬编码模式下忽略）
     *
     * @param bucketName 存储桶名称
     */
    public static void setBucketName(String bucketName) {
        log.debug("使用硬编码OSS配置，忽略setBucketName调用: {}", bucketName);
    }

    public static void setActive(String active) {
        OssSignedUrlUtil.active = active;
        log.debug("设置环境为: {}, 使用硬编码OSS配置", active);
    }

    public static String getActive() {
        return active;
    }

    /**
     * 设置区域（硬编码模式下忽略）
     *
     * @param region OSS区域（例如：oss-cn-hangzhou）
     */
    public static void setRegion(String region) {
        log.debug("使用硬编码OSS配置，忽略setRegion调用: {}", region);
    }

    public static void setStaticDomain(String staticDomain) {
        log.debug("使用硬编码OSS配置，忽略setStaticDomain调用: {}", staticDomain);
    }

    public static String getStaticDomain() {
        return staticDomain;
    }

    /**
     * 设置公共访问域名（硬编码模式下忽略）
     *
     * @param publicDomain 公共访问域名
     */
    public static void setPublicDomain(String publicDomain) {
        log.debug("使用硬编码OSS配置，忽略setPublicDomain调用: {}", publicDomain);
    }

    /**
     * 获取OSS客户端实例
     *
     * @return OSS客户端实例
     */
    private static OSS getOssClient() {
        if (ossClient == null) {
            initOssClient();
        }
        return ossClient;
    }

    /**
     * 初始化OSS客户端
     */
    private static synchronized void initOssClient() {
        if (ossClient != null) {
            return;
        }


        try {
            DefaultCredentialProvider credentialsProvider = new DefaultCredentialProvider(accessKeyId, accessKeySecret);

            // 创建OSSClient实例
            ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
            // 支持自定义域名
            clientBuilderConfiguration.setSupportCname(true);
            // 显式声明使用 V4 签名算法
            clientBuilderConfiguration.setSignatureVersion(SignVersion.V2);
            ossClient = OSSClientBuilder.create()
                    .endpoint(publicDomain)
                    .credentialsProvider(credentialsProvider)
                    .clientConfiguration(clientBuilderConfiguration)
                    .region(region)
                    .build();

            log.debug("OSS客户端初始化成功，使用域名：{}", StringUtils.isNotEmpty(publicDomain) ? publicDomain : endpoint);
        } catch (ClientException ce) {
            log.error("OSS客户端初始化失败 - 客户端异常：{}", ce.getMessage());
            throw new RuntimeException("OSS客户端初始化失败", ce);
        } catch (Exception e) {
            log.error("OSS客户端初始化失败：", e);
            throw new RuntimeException("OSS客户端初始化失败", e);
        }
    }

    // private static OSS initOSSClient(String endpoint, String accessKeyId, String accessKeySecret) {
    //     if (ossClient == null) {
    //         // 私有云要关闭CNAME
    //         ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
    //         conf.setSupportCname(false);
    //         // 使用正确的链式调用方式
    //         ossClient = OSSClientBuilder.create()
    //                 .endpoint(endpoint)
    //                 .credentialsProvider(new DefaultCredentialProvider(accessKeyId, accessKeySecret))
    //                 .clientConfiguration(conf)
    //                 .region(region)
    //                 .build();
    //     }
    //     return ossClient;
    // }

    /**
     * 关闭OSS客户端
     */
    public static void shutdown() {
        if (ossClient != null) {
            ossClient.shutdown();
            ossClient = null;
        }
    }

    /**
     * 生成预签名URL
     *
     * @param objectName 对象名称
     * @param expireTime 过期时间（毫秒）
     * @return 预签名URL
     */
    public static String generatePresignedUrl(String objectName, long expireTime) {
        return generatePresignedUrl(bucketName, objectName, expireTime);
    }

    /**
     * 生成预签名URL，使用指定的存储桶
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expireTime 过期时间（毫秒）
     * @return 预签名URL
     */
    public static String generatePresignedUrl(String bucketName, String objectName, long expireTime) {
        if (StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(objectName)) {
            log.error("生成预签名URL失败：存储桶名称或对象名称为空");
            return null;
        }

        OSS client = getOssClient();
        try {
            // 设置URL过期时间
            Date expiration = new Date(System.currentTimeMillis() + expireTime);
            // 生成预签名URL
            URL url = client.generatePresignedUrl(bucketName, objectName, expiration);
            // System.out.println("url:" + url.toString());
            return url.toString();
        } catch (OSSException oe) {
            log.error("生成预签名URL失败 - OSS异常：{}, 错误码：{}, 请求ID：{}",
                    oe.getErrorMessage(), oe.getErrorCode(), oe.getRequestId());
            return null;
        } catch (ClientException ce) {
            log.error("生成预签名URL失败 - 客户端异常：{}", ce.getMessage());
            return null;
        } catch (Exception e) {
            log.error("生成预签名URL失败：", e);
            return null;
        }
    }

    /**
     * 生成预签名URL，使用默认存储桶，默认7天过期时间
     *
     * @param objectName 对象名称
     * @return 预签名URL
     */
    public static String generatePresignedUrl(String objectName) {
        // 默认7天过期时间
        return generatePresignedUrl(objectName, 7 * 24 * 60 * 60 * 1000L);
    }

    /**
     * 生成预签名URL，适用于已经经过转发的外网地址
     *
     * @param formalUrl    转发后的完整URL
     * @param originPrefix 原始前缀
     * @param newPrefix    转发后的前缀
     * @return 预签名URL
     */
    public static String generatePresignedUrlWithPrefix(String formalUrl, String originPrefix, String newPrefix) {
        if (StringUtils.isEmpty(formalUrl) || StringUtils.isEmpty(originPrefix) || StringUtils.isEmpty(newPrefix)) {
            log.error("生成预签名URL失败：参数不完整");
            return null;
        }

        try {
            String objectName = formalUrl.replace(originPrefix + "/", "");
            log.debug("objectName:" + objectName);
            String url = generatePresignedUrl(objectName, 7 * 24 * 60 * 60 * 1000L);
            if (url != null) {
                log.debug("url:" + url);
                return url.replace(originPrefix, newPrefix);
            }
            return null;
        } catch (Exception e) {
            log.error("生成带前缀的预签名URL失败：", e);
            return null;
        }
    }

    /**
     * 生成预签名URL，使用项目配置的staticDomain
     *
     * @param formalUrl 转发后的完整URL
     * @return 预签名URL
     */
    public static String generatePresignedUrlWithPublicDomain(String formalUrl) {

        if (!"prod".equals(active) && !formalUrl.contains(staticDomain)) {
            return formalUrl;
        }

//        log.debug("staticDomain:" + staticDomain);
//        log.debug("publicDomain:" + publicDomain);
        return generatePresignedUrlWithPrefix(formalUrl, staticDomain, publicDomain);
    }

}
