package com.concise.common.file.util;

import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectResult;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 阿里云 oss 上传工具类
 * @Date: 2019/5/10
 */
@Slf4j
public class OssBootUtil {

    private static String endPoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String staticDomain;
    private static String publicDomain;
    private static String active;

    public static void setEndPoint(String endPoint) {
        OssBootUtil.endPoint = endPoint;
    }

    public static void setAccessKeyId(String accessKeyId) {
        OssBootUtil.accessKeyId = accessKeyId;
    }

    public static void setAccessKeySecret(String accessKeySecret) {
        OssBootUtil.accessKeySecret = accessKeySecret;
    }

    public static void setBucketName(String bucketName) {
        OssBootUtil.bucketName = bucketName;
    }

    public static void setStaticDomain(String staticDomain) {
        OssBootUtil.staticDomain = staticDomain;
    }

    public static void setActive(String active) {
        OssBootUtil.active = active;
    }

    public static String getStaticDomain() {
        return staticDomain;
    }

    public static String getEndPoint() {
        return endPoint;
    }

    public static String getAccessKeyId() {
        return accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    public static String getBucketName() {
        return bucketName;
    }

    public static OSSClient getOssClient() {
        return ossClient;
    }

    public static String getPublicDomain() {
        return publicDomain;
    }

    public static String getActive() {
        return active;
    }

    public static void setPublicDomain(String publicDomain) {
        OssBootUtil.publicDomain = publicDomain;
    }

    /**
     * oss 工具客户端
     */
    private static OSSClient ossClient = null;

    private static String folder;

    public static void setFolder(String folder) {
        OssBootUtil.folder = folder;
    }

    /**
     * 删除文件
     *
     * @param url
     */
    public static void deleteUrl(String url, String bucketName, String endPoint) {
        String bucketUrl = "https://" + bucketName + "." + endPoint + "/";
        url = url.replace(bucketUrl, "");
        ossClient.deleteObject(bucketName, url);
    }


    /**
     * 上传文件至阿里云 OSS
     * 文件上传成功,返回文件完整访问路径
     * 文件上传失败,返回 null
     *
     * @param file    待上传文件
     * @param fileDir 文件保存目录
     * @return oss 中的相对文件路径
     */
    public static String upload(MultipartFile file, String fileDir, String customBucket, String endPoint, String accessKeyId, String accessKeySecret, String bucketName, String fileId) {
        String FILE_URL = null;
        initOSS(endPoint, accessKeyId, accessKeySecret);
        StringBuilder fileUrl = new StringBuilder();
        String newBucket = bucketName;
        if (StringUtils.isNotEmpty(customBucket)) {
            newBucket = customBucket;
        }
        try {
            //判断桶是否存在,不存在则创建桶
            if (!ossClient.doesBucketExist(newBucket)) {
                ossClient.createBucket(newBucket);
            }
            // 获取文件名
            String orgName = file.getOriginalFilename();
            if ("" == orgName) {
                orgName = file.getName();
            }
            orgName = getFileName(orgName);
            String fileName = fileId + "_" + System.currentTimeMillis() + orgName.substring(orgName.indexOf("."));
            if (!fileDir.endsWith("/")) {
                fileDir = fileDir.concat("/");
            }
            fileUrl = fileUrl.append(fileDir + fileName);

            FILE_URL = "https://" + newBucket + "." + endPoint + "/" + fileUrl;

            PutObjectResult result = ossClient.putObject(newBucket, fileUrl.toString(), file.getInputStream());
            // 设置权限(公开读)
//            ossClient.setBucketAcl(newBucket, CannedAccessControlList.PublicRead);
            if (result != null) {
                log.info("------OSS文件上传成功------" + fileUrl);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return FILE_URL;
    }

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    public static String upload(MultipartFile file, String endPoint, String accessKeyId, String accessKeySecret, String bucketName, String folder, String fileId) {
        return upload(file, folder, null, endPoint, accessKeyId, accessKeySecret, bucketName, fileId);
    }

    /**
     * 初始化 oss 客户端
     *
     * @return
     */
    private static OSSClient initOSS(String endpoint, String accessKeyId, String accessKeySecret) {
        if (ossClient == null) {
            // 私有云要关闭CNAME
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            conf.setSupportCname(false);
            ossClient = (OSSClient) new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, conf);
        }
        return ossClient;
    }

    /**
     * 判断文件名是否带盘符，重新处理
     *
     * @param fileName
     * @return
     */
    public static String getFileName(String fileName) {
        //判断是否带有盘符信息
        // Check for Unix-style path
        int unixSep = fileName.lastIndexOf('/');
        // Check for Windows-style path
        int winSep = fileName.lastIndexOf('\\');
        // Cut off at latest possible point
        int pos = (winSep > unixSep ? winSep : unixSep);
        if (pos != -1) {
            // Any sort of path separator found...
            fileName = fileName.substring(pos + 1);
        }
        //替换上传文件名字的特殊字符
        fileName = fileName.replace("=", "").replace(",", "").replace("&", "").replace("#", "");
        return fileName;
    }

    public static String filter(String str) throws PatternSyntaxException {
        // 清除掉所有特殊字符
        String regEx = "[`_《》~!@#$%^&*()+=|{}':;',\\[\\].<>?~！@#￥%……&*（）——+|{}【】'；：\"\"'。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }




    /**
     * 从OSS获取文件输入流
     *
     * @param objectName OSS中的对象名称
     * @return 文件输入流
     */
    public static java.io.InputStream getInputStream(String objectName) {
        try {
            if (ossClient == null) {
                initOSS(endPoint, accessKeyId, accessKeySecret);
            }

            // 如果objectName是完整URL，则需要提取出OSS中的对象名称
            if (objectName.startsWith("http")) {
                String bucketUrl = "https://" + bucketName + "." + endPoint + "/";
                objectName = objectName.replace(bucketUrl, "");
            }

            // 获取OSS对象
            com.aliyun.oss.model.OSSObject ossObject = ossClient.getObject(bucketName, objectName);
            return ossObject.getObjectContent();
        } catch (Exception e) {
            log.error("从OSS获取文件输入流失败：" + e.getMessage(), e);
            return null;
        }
    }
}
