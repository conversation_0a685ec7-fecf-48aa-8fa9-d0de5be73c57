package com.concise.common.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-02-27 10:38
 */
public class FileUtils extends FileUtil {

    /**
     * 获取文件后缀
     *
     * @param fileName
     *
     * @return
     */
    public static String getSuffix(String fileName) {
        return fileName.substring(fileName.lastIndexOf("."));
    }


    /**
     * 复制文件
     *
     * @param oldFilePath 源文件地址及文件名称
     * @param newPath     新目录
     * @param newName     复制的新文件名称
     *
     * @throws IOException
     */
    public static void copyFile(String oldFilePath, String newPath, String newName) throws IOException {
        File oldFile = new File(oldFilePath);
        //创建新抽象文件
        File newfile = new File(newPath + newName);
        if (!oldFile.exists() || !oldFile.isFile()) {
            return;
        }
        if (newfile.exists()) {
            newfile.delete();
        }
        newfile.createNewFile();
        try {
            FileInputStream fin = new FileInputStream(oldFile);
            try {
                FileOutputStream fout = new FileOutputStream(newfile, true);
                byte[] b = new byte[1024];
                try {
                    while ((fin.read(b)) != -1) {
                        fout.write(b);
                    }
                    fin.close();
                    fout.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }


    /**
     * 生成新的文件名
     *
     * @param fileOriginName 源文件名
     *
     * @return
     */
    public static String getFileName(String fileOriginName) {
        return IdUtil.fastSimpleUUID() + FileUtils.getSuffix(fileOriginName);
    }

    /**
     * 文件进行类型转换
     *
     * @param multipartFile
     *
     * @return
     *
     * @throws IOException
     */
    public static File multipartFileToFile(MultipartFile multipartFile) {
        File file;
        if (null != multipartFile && multipartFile.getSize() > 0) {
            InputStream ins;
            try {
                ins = multipartFile.getInputStream();
                file = new File(FileUtil.getTmpDirPath(), Objects.requireNonNull(multipartFile.getOriginalFilename()));
                OutputStream os = new FileOutputStream(file);
                int bytesRead;
                byte[] buffer = new byte[8192];
                while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.close();
                ins.close();
                return file;
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        return null;
    }
    // /var/folders/2r/8vqdrbtx3sl7cz2k94m45h8c0000gn/T/QQ20190921-231921_1647831261064.png
    // /var/folders/2r/8vqdrbtx3sl7cz2k94m45h8c0000gn/T/QQ20190921-231921_1647831261064.png
    // /var/folders/2r/8vqdrbtx3sl7cz2k94m45h8c0000gn/T/QQ20190921-231921_1647831261064.png
    // /var/folders/2r/8vqdrbtx3sl7cz2k94m45h8c0000gn/T/QQ20190921-231921_1647831261064.png

    /**
     * 初始化文件名称
     * 防止出现相同的文件 导致对象存储覆盖
     * 返回文件名
     *
     * @param multipartFile
     *
     * @return
     */
    @SneakyThrows
    public static String initFileName(MultipartFile multipartFile) {
        String originalFilename = multipartFile.getOriginalFilename();
        return FileUtil.getPrefix(originalFilename) + StrUtil.UNDERLINE + RandomUtil.randomString(4) + StrUtil.DOT + FileUtil.extName(originalFilename);
    }

    public static String initFileName(File toFile) {
        String originalFilename = toFile.getName();
        return FileUtil.getPrefix(originalFilename) + StrUtil.UNDERLINE + RandomUtil.randomString(4) + StrUtil.DOT + FileUtil.extName(originalFilename);
    }


    /**
     * 获取原始名称
     *
     * @param multipartFile
     *
     * @return
     */
    @SneakyThrows
    public static String getFileName(MultipartFile multipartFile) {
        File file = multipartFileToFile(multipartFile);
        assert file != null;
        return file.getName();
    }

}
