package com.concise.common.util;

import com.alibaba.fastjson.JSONObject;
import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import sun.misc.BASE64Encoder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.security.Key;

/**
 * 电子签章工具类(天印签章)
 *
 * <AUTHOR>
 */
@Slf4j
public class ElectronicSignatureUtil {
    //天印服务器接口信息
    private static String ProjectID = "330001246";
    private static String ProjectSecret = "a7c365e030894716a89976a3b504942f";
    private static String ServerIP = "http://*************";

    private static String Port = "9999";

//    //正式接口信息
//    private static String ProjectID = "918245a6865c43639843d2db8aeada50";
//    private static String ProjectSecret = "52fbef9354c24353956e3f5d84e22e33";
//    //ip: http://***********
//    private static String ServerIP = "https://zjyz.zjzwfw.gov.cn";
//
//    private static String Port = "10000";

    /**
     * 获取盖章后的pdf的base64文件
     *
     * @param base64   原pdf的base64
     * @param sealSn   印章编号
     * @param fileName 原pdf文件名称
     * @return
     */
    public static String signedBase64(String base64, String sealSn, String fileName, String signType, String posX, String posY) {
        JSONObject signPdf = createSignPdf(base64, sealSn, fileName, signType, posX, posY);
        Boolean success = signPdf.getBoolean("success");
        if (success) {
            JSONObject jsonObject = signPdf.getJSONObject("data");
            return jsonObject.getString("signFileB64").replaceAll("\\n", "");
        } else {
            throw new RuntimeException(signPdf.getString("message"));
        }
    }

    /**
     * * pdf文件盖章
     * * 接口地址：/V1/accounts/outerAccounts/create
     *
     * @param base64   pdf文件base64
     * @param sealSn   印章编号
     * @param fileName pdf文件名
     * @param signType 签署类型 1 单页签 2多页签 3 骑缝签 4关键字签
     * @param posX     x坐标
     * @param posY     y坐标
     * @return
     */
    public static JSONObject createSignPdf(String base64, String sealSn, String fileName, String signType, String posX, String posY) {
        JSONObject obj = null;
        String resp = null;
        try {
            JSONObject reqData = new JSONObject();
            reqData.put("fileBase64", base64);
            reqData.put("sealSn", sealSn);
            reqData.put("posX", posX);
            reqData.put("posY", posY);
            reqData.put("signType", signType);
            //reqData.put("key", "单位");
            //页码，若为关键字定位此项无需传递，默认1
            reqData.put("posPage", "1");
            reqData.put("fileName", fileName);
            resp = post("/seal-platform/seal/v1/rest/sign/signPdf", reqData);
            obj = JSONObject.parseObject(resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }


    public static String post(String apiUrl, JSONObject data) throws Exception {

        apiUrl = ServerIP + ":" + Port + apiUrl;

        // 请求数据转为JSON字节流,作为HTTP请求体
        String myData = data.toString();
        System.out.println(myData);
        byte[] stream = data.toString().getBytes("UTF-8");
        // 签名数据,根据签名算法,对请求数据进行签名
        String signature = sign(stream);
        //System.out.println(signature);
        // 设置HTTP请求头
        HttpEntityEnclosingRequestBase req = new HttpPost(apiUrl);
        // project-id为用户的projectId
        req.addHeader("appId", ProjectID);
        // signature为之前生成的签名
        req.addHeader("signature", signature);
        // 签名模式：1、sort-parameters——对参数名称按ASCII码排序后签名，2、package——对整个httpbody签名。默认为package模式
        //req.addHeader("X-timevale-mode", "package");
        // 签名算法：1、HMAC-SHA256，2、RSA。默认为HMAC-SHA256
        req.addHeader("Content-Type", "application/json");

        // 设置HTTP请求体
        HttpEntity entity = new ByteArrayEntity(stream, ContentType
                .create(ContentType.APPLICATION_JSON.getMimeType(), "UTF-8"));
        req.setEntity(entity);

        // 执行请求
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient cli = httpClientBuilder.build();
        HttpResponse res = cli.execute(req);
        int statusCode = res.getStatusLine().getStatusCode();
        log.debug("返回状态：" + statusCode);
        if (200 != statusCode) {
            System.out.println(statusCode);
        }
        // 获取响应
        InputStream in = res.getEntity().getContent();

        byte[] resp = readStream(in);
        String strRes = new String(resp, "UTF-8");
        log.debug("返回内容：" + strRes);
        cli.close();
        return strRes;
    }

    private static String sign(byte[] stream)
            throws Exception {
        // 获取消息验证码类的实例，算法选择"HmacSHA256"
        Mac mac = Mac.getInstance("HmacSHA256");

        // 获取安全密钥
        Key secKey = new SecretKeySpec(
                ProjectSecret.getBytes("UTF-8"),
                mac.getAlgorithm());

        // 初始化
        mac.init(secKey);

        // 获得签名
        byte[] sign = mac.doFinal(stream);

        // 将byte[]格式的签名用binary编码转化为字符串返回
        return binaryEncode(sign);

    }

    public static String binaryEncode(byte[] data) {
        final char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8',
                '9', 'a', 'b', 'c', 'd', 'e', 'f'};

        StringBuilder builder = new StringBuilder();

        for (byte i : data) {
            builder.append(hexDigits[i >>> 4 & 0xf]);
            builder.append(hexDigits[i & 0xf]);
        }

        return builder.toString();
    }

    public static byte[] readStream(InputStream in) throws IOException {

        ByteArrayOutputStream output = new ByteArrayOutputStream();

        byte[] buffer = new byte[1024 * 10];
        try {

            int n = 0;
            while ((n = in.read(buffer)) != -1) {
                output.write(buffer, 0, n);
            }

            return output.toByteArray();

        } finally {
            in.close();
            output.close();
        }
    }

    public static String PDFToBase64(File file) {
        BASE64Encoder encoder = new BASE64Encoder();
        FileInputStream fin = null;
        BufferedInputStream bin = null;
        ByteArrayOutputStream baos = null;
        BufferedOutputStream bout = null;
        try {
            fin = new FileInputStream(file);
            bin = new BufferedInputStream(fin);
            baos = new ByteArrayOutputStream();
            bout = new BufferedOutputStream(baos);
            byte[] buffer = new byte[1024];
            int len = bin.read(buffer);
            while (len != -1) {
                bout.write(buffer, 0, len);
                len = bin.read(buffer);
            }
            //刷新此输出流并强制写出所有缓冲的输出字节
            bout.flush();
            byte[] bytes = baos.toByteArray();
            return Base64.encode(bytes);
            //return encoder.encodeBuffer(bytes);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fin.close();
                bin.close();
                bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
