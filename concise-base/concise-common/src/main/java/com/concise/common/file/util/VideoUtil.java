package com.concise.common.file.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

public class VideoUtil {

    //获取视频第十帧，避免出现黑屏的情况
    private static final int GET_FRAMES_LENGTH = 50;


    public static MultipartFile getScreenshot(MultipartFile file) {

        FFmpegFrameGrabber grabber;
        File savefile = null;
        try {
            // 使用下面的jar包
            String originalFilename = file.getOriginalFilename();
            savefile = new File(FileUtil.getTmpDirPath() + "/" + IdUtil.randomUUID() + originalFilename);
            FileCopyUtils.copy(file.getBytes(), savefile);
            grabber = FFmpegFrameGrabber.createDefault(savefile);
            grabber.start();
            // 视频总帧数
            int videoLength = grabber.getLengthInFrames();

            Frame frame = null;
            int i = 0;
            while (i < videoLength) {
                // 过滤前10帧,避免出现全黑的图片,依自己情况而定(每循环一次取一帧)
                frame = grabber.grabFrame();
                if ((i > GET_FRAMES_LENGTH) && (frame.image != null)) {
                    break;
                }
                i++;
            }

            // 视频旋转度
            String rotate = grabber.getVideoMetadata("rotate");
            Java2DFrameConverter converter = new Java2DFrameConverter();
            // 绘制图片
            BufferedImage bi = converter.getBufferedImage(frame);
            // 旋转图片
            if (rotate != null) {
                bi = rotate(bi, Integer.parseInt(rotate));
            }
            // 图片的类型
            String imageMat = "jpg";

            // 创建文件
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(bi, imageMat, os);
            InputStream input = new ByteArrayInputStream(os.toByteArray());
            String screenShotName = originalFilename + "_screenshot.jpg";
            MultipartFile multipartFile = new MockMultipartFile(screenShotName, screenShotName, screenShotName, input);

            grabber.stop();
            FileUtil.del(savefile);
            return multipartFile;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
        }

        return null;
    }


    private static BufferedImage rotate(BufferedImage src, int angel) {
        int src_width = src.getWidth(null);
        int src_height = src.getHeight(null);
        int type = src.getColorModel().getTransparency();
        Rectangle rect_des = calcRotatedSize(new Rectangle(new Dimension(src_width, src_height)), angel);
        BufferedImage bi = new BufferedImage(rect_des.width, rect_des.height, type);
        Graphics2D g2 = bi.createGraphics();
        g2.translate((rect_des.width - src_width) / 2, (rect_des.height - src_height) / 2);
        g2.rotate(Math.toRadians(angel), src_width / 2, src_height / 2);
        g2.drawImage(src, 0, 0, null);
        g2.dispose();
        return bi;
    }


    private static Rectangle calcRotatedSize(Rectangle src, int angel) {
        if (angel >= 90) {
            if (angel / 90 % 2 == 1) {
                int temp = src.height;
                src.height = src.width;
                src.width = temp;
            }
            angel = angel % 90;
        }
        double r = Math.sqrt(src.height * src.height + src.width * src.width) / 2;
        double len = 2 * Math.sin(Math.toRadians(angel) / 2) * r;
        double angel_alpha = (Math.PI - Math.toRadians(angel)) / 2;
        double angel_dalta_width = Math.atan((double) src.height / src.width);
        double angel_dalta_height = Math.atan((double) src.width / src.height);
        int len_dalta_width = (int) (len * Math.cos(Math.PI - angel_alpha - angel_dalta_width));
        int len_dalta_height = (int) (len * Math.cos(Math.PI - angel_alpha - angel_dalta_height));
        int des_width = src.width + len_dalta_width * 2;
        int des_height = src.height + len_dalta_height * 2;
        return new java.awt.Rectangle(new Dimension(des_width, des_height));
    }

    public static MultipartFile getVideoAsMultipartFile(String videoUrl) throws Exception {
        URL url = new URL(videoUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.connect();

        if (connection.getResponseCode() != 200) {
            throw new RuntimeException("HTTP错误码: " + connection.getResponseCode());
        }

        InputStream inputStream = connection.getInputStream();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        byte[] buffer = new byte[4096];
        int bytesRead;

        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }

        inputStream.close();

        // 转换为 MultipartFile
        byte[] videoBytes = outputStream.toByteArray();
        return new MockMultipartFile("file", "video.mp4", "video/mp4", videoBytes);
    }
}
