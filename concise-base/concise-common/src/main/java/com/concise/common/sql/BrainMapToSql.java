package com.concise.common.sql;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;

import java.util.*;

/**
 * <p>
 * 通过脑图生成SQL, 仅支持Markdown语法 <br>
 * 若解析失败请检查<br>
 * 1. md文件是否存在<br>
 * 2. md文件内容是否符合格式要求<br>
 * &nbsp 1) 表名使用`## `<br>
 * &nbsp 2) 字段名使用`- `<br>
 * &nbsp 3) 类型紧跟在字段后面,并且使用`  @`间隔开<br>
 * &nbsp 4) 注释紧跟在字段或者类型后面,并且使用`  #`间隔开<br>
 * -    类型留空会默认为varchar 类型;
 * 若表为二级标题更改 `level` 为 `##`,同理三级标题改为 `###`
 * 推荐搭配脑图使用, 脑图->导出为Markdown(也有一些脑图是导出标记)->将文件内容格式检查(去除大标题等等等..)->copy到BrainMap.md文件<br>
 * <p>
 * 之前画完脑图之后在建立数据库的过程中还需要在写一遍字段名和注释,索性写了这个工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020/7/7 14:13
 * @Description 脑图转SQL工具类
 */
public class BrainMapToSql {

    static String primaryKey = "";
    static String tableName = "";
    static String tableNote = "";

    /**
     * 解析等级
     * 观察导出的Markdown文件中的表前缀
     * 若表为二级标题更改 `level` 为 `##`,同理三级标题改为 `###`
     */
    static String level = "##";

    /**
     * decimal精度设置
     */
    static int decimalLength = 10;
    static int decimalAccuracy = 2;

    public static void main(String[] args) {
        String content = null;
        String path = null;
        try {
            path = System.getProperty("user.dir") + "/concise-base/concise-common/src/main/java/com/concise/common/sql";
            content = FileUtil.readString(path + "/BrainMap.md", "utf-8");
        } catch (IORuntimeException e) {
            e.printStackTrace();
        }

        if (StrUtil.isNotBlank(content)) {
            String[] modelList = Objects.requireNonNull(content).split(level);
            StringBuilder resultBuffer = new StringBuilder();
            resultBuffer.append("/**\n" + " * <AUTHOR> + " * @version 1.0.0\n" + " * @createTime ")
                    .append(DateUtil.now()).append("\n")
                    .append(" * @Description 脑图转SQL工具类\n").append(" */\n");
            List<StringBuffer> stringBufferList = parsingMd(modelList);

            stringBufferList.forEach(resultBuffer::append);
            FileUtil.writeUtf8String(resultBuffer.toString(), path + "/generateSql.sql");
            System.out.println("SQLFile generated successfully! path: " + path + "/generateSql.sql");
        } else {
            System.out.println("no content read");

        }

    }

    /**
     * 解析
     *
     * @param modelList
     * @return
     */
    private static List<StringBuffer> parsingMd(String[] modelList) {
        List<StringBuffer> stringBufferList = new ArrayList<>();
        for (int i1 = 1; i1 < modelList.length; i1++) {
            String testOne = modelList[i1];
            String[] modelOne = testOne.split("\n" +
                    "\n" +
                    "-");
            List<Map<String, String>> mapList = getSqlModelList(modelOne);
            tableName = String.valueOf(mapList.get(0).keySet().toArray()[0]);
            tableNote = String.valueOf(mapList.get(0).values().toArray()[0]);
            StringBuffer sqlBuffer = new StringBuffer();
            sqlBuffer.append("DROP TABLE IF EXISTS `").append(tableName).append("`;");
            sqlBuffer.append("\n" + "CREATE TABLE `").append(tableName).append("` (");
            mapList.remove(0);
            mapList.forEach(o -> o.forEach((key, value) -> {
                if (value.contains("\n")) {
                    value = StrUtil.removeAllLineBreaks(value);
                }
                sqlSplice(sqlBuffer, key, value);
            }));
            if (sqlBuffer.indexOf("status") < 0) {
                sqlBuffer.append("\n"
                        // + "  `status` varchar(255) NOT NULL DEFAULT 'normal' COMMENT '数据状态',\n"
                        + "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',");
            }
            sqlBuffer.append("\n"
                    + "  `create_user` varchar(255) NULL COMMENT '创建人',\n"
                    + "  `update_user` varchar(255) NULL COMMENT '最后更新人',\n"
                    + "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n"
                    + "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" + "  PRIMARY KEY (`")
                    .append(primaryKey)
                    .append("`)\n")
                    .append(") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='")
                    .append(tableNote)
                    .append("';")
                    .append("\n")
                    .append("SET FOREIGN_KEY_CHECKS = 1;\n")
                    .append("\n");
            stringBufferList.add(sqlBuffer);
        }
        return stringBufferList;
    }


    /**
     * 负责拼接SQL
     *
     * @param sqlBuffer
     * @param key
     * @param value
     */
    private static void sqlSplice(StringBuffer sqlBuffer, String key, String value) {

        System.err.println(key);
        System.err.println(value);

        String[] split = key.split("@");
        key = split[0].trim();
        if (split.length > 1) {
            String type = split[1].trim();
            if (type.equals("文本")) {
                sqlBuffer.append("\n" + "  `").append(key);
                if (value.equals("主键")) {
                    primaryKey = key;
                    sqlBuffer.append("` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '").append(tableNote + value).append("',");
                } else {
                    sqlBuffer.append("` varchar(255) DEFAULT NULL COMMENT '").append(value).append("',");
                }
            }
            if (type.equals("数字")) {
                sqlBuffer.append("\n" + "  `").append(key).append("` bigint(20) DEFAULT NULL COMMENT '").append(value).append("',");
            }
        } else {
            if (value.equals("主键")) {
                primaryKey = key;
                sqlBuffer.append("\n" + "  `").append(key).append("` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '").append(tableNote + value).append("',");
            } else {
                buildMiddleSql(sqlBuffer, key, value);
            }
        }

    }

    public static void buildMiddleSql(StringBuffer sqlBuffer, String key, String value) {
        if (key.endsWith("id")) {
            sqlBuffer.append("\n" + "  `").append(key).append("` bigint(20) DEFAULT NULL COMMENT '").append(value).append("',");
        } else if (key.endsWith("time")) {
            sqlBuffer.append("\n" + "  `").append(key).append("` datetime DEFAULT NULL COMMENT '").append(value).append("',");
        } else if (key.endsWith("money") || value.endsWith("金额") || value.endsWith("单价")) {
            sqlBuffer.append("\n" + "  `").append(key).append("` decimal(").append(decimalLength).append(",").append(decimalAccuracy).append(") DEFAULT NULL COMMENT '").append(value).append("',");
        } else if (value.endsWith("数量")) {
            sqlBuffer.append("\n" + "  `").append(key).append("` int(11) DEFAULT NULL COMMENT '").append(value).append("',");
        } else if (value.endsWith("面积")) {
            sqlBuffer.append("\n" + "  `").append(key).append("` double(255,2) DEFAULT NULL COMMENT '").append(value).append("',");
        } else {
            sqlBuffer.append("\n" + "  `").append(key).append("` varchar(255) DEFAULT NULL COMMENT '").append(value).append("',");
        }
    }

    /**
     * 解析单个表
     *
     * @param modelOne
     * @return
     */
    private static List<Map<String, String>> getSqlModelList(String[] modelOne) {
        List<Map<String, String>> mapList = new ArrayList<>();
        for (String s : modelOne) {
            String[] split = s.split(" #");
            Map<String, String> stringStringMap = new HashMap<>();
            if (split.length <= 1) {
                stringStringMap.put(split[0].trim(), "主键");
            } else {
                stringStringMap.put(split[0].trim(), split[1].trim());
            }
            mapList.add(stringStringMap);
        }
        return mapList;
    }
}
