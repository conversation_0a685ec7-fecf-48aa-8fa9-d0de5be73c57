package com.concise.common.pojo.base.node;

import java.util.List;

/**
 * 树节点接口
 *
 * <AUTHOR>
 * @date 2020/4/5 14:07
 */
public interface BaseTreeStringNode {


    /**
     * 获取节点id
     *
     * @return 节点id
     * <AUTHOR>
     * @date 2020/7/9 18:36
     */
    String getId();

    /**
     * 获取节点父id
     *
     * @return 节点父id
     * <AUTHOR>
     * @date 2020/7/9 18:36
     */
    String getPid();

    /**
     * 设置children
     *
     * @param children 子节点集合
     * <AUTHOR>
     * @date 2020/7/9 18:36
     */
    void setChildren(List children);
}
