package com.concise.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.common.pojo.page.PageResult;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 分页工具类针对hutool分页的扩展
 *
 * <AUTHOR>
 * @date 2020/9/19 10:30
 **/
public class PageUtil<T> extends cn.hutool.core.util.PageUtil{
    /**
     * 每页大小（默认20）
     */
    private static final String PAGE_SIZE_PARAM_NAME = "pageSize";

    /**
     * 第几页（从1开始）
     */
    private static final String PAGE_NO_PARAM_NAME = "pageNo";
    /**
     * 逻辑分页
     *
     * <AUTHOR>
     * @date 2020/9/19 10:36
     **/
    public static <T> List<T> page(Page<T> page, List<T> list) {
        setFirstPageNo(1);
        int start = getStart(Convert.toInt(page.getCurrent()), Convert.toInt(page.getSize()));
        int end = getEnd(Convert.toInt(page.getCurrent()), Convert.toInt(page.getSize()));
        if(start > list.size()) {
            return CollectionUtil.newArrayList();
        }else if(start > end) {
            return CollectionUtil.newArrayList();
        } else if(end > list.size()) {
            return list.subList(start, list.size());
        } else {
            return list.subList(start, end);
        }
    }

    public static <T> PageResult<T> getPageResult(List<T> list) {
        int pageNo=1;
        int pageSize=20;
        HttpServletRequest request = HttpServletUtil.getRequest();

        //每页条数
        String pageSizeString = request.getParameter(PAGE_SIZE_PARAM_NAME);
        if (ObjectUtil.isNotEmpty(pageSizeString)) {
            pageSize = Integer.parseInt(pageSizeString);
        }

        //第几页
        String pageNoString = request.getParameter(PAGE_NO_PARAM_NAME);
        if (ObjectUtil.isNotEmpty(pageNoString)) {
            pageNo = Integer.parseInt(pageNoString);
        }

        // 构造PageResult对象
        PageResult<T> pageResult = new PageResult<>();

        // 计算总条数
        int totalCount = list.size();
        pageResult.setTotalRows(totalCount);

        // 计算总页数
        int totalPages = (totalCount + pageSize - 1) / pageSize;
        pageResult.setTotalPage(totalPages);

        // 设置当前页码
        pageResult.setPageNo(pageNo);

        // 设置每页显示条数
        pageResult.setPageSize(pageSize);

        // 计算分页数据
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalCount);
        List<T> pageData = new ArrayList<>(list.subList(startIndex, endIndex));
        pageResult.setRows(pageData);

        return pageResult;
    }
}
