package com.concise.common.file.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.config.FileConfig;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.context.requestno.RequestNoContext;
import com.concise.common.exception.LibreOfficeException;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.FileOperator;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.enums.FileLocationEnum;
import com.concise.common.file.enums.SysFileInfoExceptionEnum;
import com.concise.common.file.mapper.SysFileInfoMapper;
import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.file.result.SysFileInfoResult;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.file.util.DownloadUtil;
import com.concise.common.file.util.OssBootUtil;
import com.concise.common.file.util.VideoUtil;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.LibreOfficeUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件信息表 服务实现类
 *
 * <AUTHOR>
 * @date 2020/6/7 22:15
 */
@Slf4j
@Service
public class SysFileInfoServiceImpl extends ServiceImpl<SysFileInfoMapper, SysFileInfo> implements SysFileInfoService {


    @Autowired
    FileOperator fileOperator;

    @Value("${aliyun.oss.endpoint}")
    private String endPoint;
    @Value("${aliyun.oss.accessKey}")
    private String accessKeyId;
    @Value("${aliyun.oss.secretKey}")
    private String accessKeySecret;
    @Value("${aliyun.oss.bucketName}")
    private String bucketName;
    @Value("${aliyun.oss.folder}")
    private String folder;
    @Value("${aliyun.oss.staticDomain}")
    private String staticDomain;
    @Value("${aliyun.oss.publicDomain}")
    private String publicDomain;

    @Value("${file.saveType}")
    private String fileSaveType;

    @Override
    public PageResult<SysFileInfo> page(SysFileInfoParam sysFileInfoParam) {

        // 构造条件
        LambdaQueryWrapper<SysFileInfo> queryWrapper = new LambdaQueryWrapper<>();

        // 拼接查询条件-文件存储位置（1:阿里云，2:腾讯云，3:minio，4:本地）
        if (ObjectUtil.isNotNull(sysFileInfoParam)) {
            if (ObjectUtil.isNotEmpty(sysFileInfoParam.getFileLocation())) {
                queryWrapper.like(SysFileInfo::getFileLocation, sysFileInfoParam.getFileLocation());
            }

            // 拼接查询条件-文件仓库
            if (ObjectUtil.isNotEmpty(sysFileInfoParam.getFileBucket())) {
                queryWrapper.like(SysFileInfo::getFileBucket, sysFileInfoParam.getFileBucket());
            }

            // 拼接查询条件-文件名称（上传时候的文件名）
            if (ObjectUtil.isNotEmpty(sysFileInfoParam.getFileOriginName())) {
                queryWrapper.like(SysFileInfo::getFileOriginName, sysFileInfoParam.getFileOriginName());
            }
        }
        queryWrapper.orderByDesc(SysFileInfo::getCreateTime);
        // 查询分页结果
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SysFileInfo> list(SysFileInfoParam sysFileInfoParam) {

        // 构造条件
        LambdaQueryWrapper<SysFileInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(sysFileInfoParam.getId())) {
            queryWrapper.eq(SysFileInfo::getId, sysFileInfoParam.getId());
        }
        if (ObjectUtil.isNotEmpty(sysFileInfoParam.getReferType())) {
            queryWrapper.eq(SysFileInfo::getReferType, sysFileInfoParam.getReferType());
        }
        if (ObjectUtil.isNotEmpty(sysFileInfoParam.getReferId())) {
            queryWrapper.eq(SysFileInfo::getReferId, sysFileInfoParam.getReferId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public void add(SysFileInfoParam sysFileInfoParam) {

        // 将dto转为实体
        SysFileInfo sysFileInfo = new SysFileInfo();
        BeanUtil.copyProperties(sysFileInfoParam, sysFileInfo);

        this.save(sysFileInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SysFileInfoParam sysFileInfoParam) {

        // 查询文件的信息
        SysFileInfo sysFileInfo = this.getById(sysFileInfoParam.getId());

        // 删除具体文件
        if ("online".equals(fileSaveType)) {
            OssBootUtil.deleteUrl(sysFileInfoParam.getFilePath(), bucketName, endPoint);
        } else {
            this.fileOperator.deleteFile(sysFileInfo.getFileBucket(), sysFileInfo.getFileObjectName());
        }

        // 删除文件记录
        this.removeById(sysFileInfoParam.getId());
    }

    @Override
    public void edit(SysFileInfoParam sysFileInfoParam) {

        // 根据id查询实体
        SysFileInfo sysFileInfo = this.querySysFileInfo(sysFileInfoParam);

        // 请求参数转化为实体
        BeanUtil.copyProperties(sysFileInfoParam, sysFileInfo);

        this.updateById(sysFileInfo);
    }

    @Override
    public SysFileInfo detail(SysFileInfoParam sysFileInfoParam) {
        return this.querySysFileInfo(sysFileInfoParam);
    }

    @Override
    public SysFileInfo uploadFile(MultipartFile file, String referType) {

        // 生成文件的唯一id
        Long fileId = IdWorker.getId();

        // 获取文件原始名称
        String originalFilename = file.getOriginalFilename();
        // 获取文件后缀
        String fileSuffix = null;

        if (ObjectUtil.isNotEmpty(originalFilename)) {
            fileSuffix = StrUtil.subAfter(originalFilename, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        // 存储文件
        byte[] bytes;
        String filePath;
        try {
            bytes = file.getBytes();
            filePath = fileOperator.storageFile(FileConfig.DEFAULT_BUCKET, finalName, bytes);
        } catch (IOException e) {
            throw new ServiceException(SysFileInfoExceptionEnum.ERROR_FILE);
        }

        // 计算文件大小kb
        long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(file.getSize()), BigDecimal.valueOf(1024))
                .setScale(0, BigDecimal.ROUND_HALF_UP));

        //计算文件大小信息
        String fileSizeInfo = FileUtil.readableFileSize(file.getSize());

        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.LOCAL.getCode());
        sysFileInfo.setFileBucket(FileConfig.DEFAULT_BUCKET);
        sysFileInfo.setFileObjectName(finalName);
        sysFileInfo.setFileOriginName(originalFilename);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFilePath(filePath);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);
        sysFileInfo.setReferType(referType);
        this.save(sysFileInfo);

        // 返回文件id
        return sysFileInfo;
    }

    @Override
    public SysFileInfoResult getFileInfoResult(Long fileId) {
        byte[] fileBytes;
        // 获取文件名
        SysFileInfo sysFileInfo = this.getById(fileId);
        if (sysFileInfo == null) {
            throw new ServiceException(SysFileInfoExceptionEnum.NOT_EXISTED_FILE);
        }
        try {
            // 返回文件字节码
            fileBytes = fileOperator.getFileBytes(FileConfig.DEFAULT_BUCKET, sysFileInfo.getFileObjectName());
        } catch (Exception e) {
            log.error(">>> 获取文件流异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), e.getMessage());
            throw new ServiceException(SysFileInfoExceptionEnum.FILE_STREAM_ERROR);
        }

        SysFileInfoResult sysFileInfoResult = new SysFileInfoResult();
        BeanUtil.copyProperties(sysFileInfo, sysFileInfoResult);
        sysFileInfoResult.setFileBytes(fileBytes);

        return sysFileInfoResult;
    }

    @Override
    public void assertFile(Long field) {
        SysFileInfo sysFileInfo = this.getById(field);
        if (ObjectUtil.isEmpty(sysFileInfo)) {
            throw new ServiceException(SysFileInfoExceptionEnum.NOT_EXISTED);
        }
    }

    @Override
    public void preview(SysFileInfoParam sysFileInfoParam, HttpServletResponse response) {

        byte[] fileBytes;
        //根据文件id获取文件信息结果集
        SysFileInfoResult sysFileInfoResult = this.getFileInfoResult(sysFileInfoParam.getId());
        //获取文件后缀
        String fileSuffix = sysFileInfoResult.getFileSuffix().toLowerCase();
        //获取文件字节码
        fileBytes = sysFileInfoResult.getFileBytes();
        //如果是图片类型，则直接输出
        if (LibreOfficeUtil.isPic(fileSuffix)) {
            try {
                //设置contentType
                response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                //获取outputStream
                ServletOutputStream outputStream = response.getOutputStream();
                //输出
                IoUtil.write(outputStream, true, fileBytes);
            } catch (IOException e) {
                throw new ServiceException(SysFileInfoExceptionEnum.PREVIEW_ERROR_NOT_SUPPORT);
            }

        } else if (LibreOfficeUtil.isDoc(fileSuffix)) {
            try {
                //如果是文档类型，则使用libreoffice转换为pdf或html
                InputStream inputStream = IoUtil.toStream(fileBytes);

                //获取目标contentType（word和ppt和text转成pdf，excel转成html)
                String targetContentType = LibreOfficeUtil.getTargetContentTypeBySuffix(fileSuffix);

                //设置contentType
                response.setContentType(targetContentType);

                //获取outputStream
                ServletOutputStream outputStream = response.getOutputStream();

                //转换
                LibreOfficeUtil.convertToPdf(inputStream, outputStream, fileSuffix);

                //输出
                IoUtil.write(outputStream, true, fileBytes);
            } catch (IOException e) {
                log.error(">>> 预览文件异常", e.getMessage());
                throw new ServiceException(SysFileInfoExceptionEnum.PREVIEW_ERROR_NOT_SUPPORT);

            } catch (LibreOfficeException e) {
                log.error(">>> 初始化LibreOffice失败", e.getMessage());
                throw new ServiceException(SysFileInfoExceptionEnum.PREVIEW_ERROR_LIBREOFFICE);
            }

        } else {
            //否则不支持预览（暂时）
            throw new ServiceException(SysFileInfoExceptionEnum.PREVIEW_ERROR_NOT_SUPPORT);
        }
    }

    @Override
    public void download(SysFileInfoParam sysFileInfoParam, HttpServletResponse response) {
        // 获取文件信息结果集
        SysFileInfoResult sysFileInfoResult = this.getFileInfoResult(sysFileInfoParam.getId());
        String fileName = sysFileInfoResult.getFileOriginName();
        byte[] fileBytes = sysFileInfoResult.getFileBytes();
        DownloadUtil.download(fileName, fileBytes, response);
    }

    /**
     * 获取文件信息表
     *
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    private SysFileInfo querySysFileInfo(SysFileInfoParam sysFileInfoParam) {
        SysFileInfo sysFileInfo = this.getById(sysFileInfoParam.getId());
        if (ObjectUtil.isEmpty(sysFileInfo)) {
            throw new ServiceException(SysFileInfoExceptionEnum.NOT_EXISTED);
        }
        return sysFileInfo;
    }

    @Override
    public SysFileInfo uploadFileOss(MultipartFile file, String referType, String referId) {
        // 生成文件的唯一id
        Long fileId = IdWorker.getId();

        String fileName = file.getOriginalFilename();
        fileName = OssBootUtil.getFileName(fileName);
        String month = DateUtil.format(DateUtil.date(), DatePattern.SIMPLE_MONTH_PATTERN);
        String fileDir = folder + "/" + month + "/";
        String url = OssBootUtil.upload(file, endPoint, accessKeyId, accessKeySecret, bucketName, fileDir, fileId.toString());

        //计算文件大小信息
        String fileSizeInfo = FileUtil.readableFileSize(file.getSize());
        // 计算文件大小kb
        long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(file.getSize()), BigDecimal.valueOf(1024))
                .setScale(0, BigDecimal.ROUND_HALF_UP));

        // 获取文件后缀
        String fileSuffix = null;

        if (ObjectUtil.isNotEmpty(fileName)) {
            fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
        }

        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileOriginName(fileName);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);
        sysFileInfo.setFilePath(url);
        sysFileInfo.setOutPath(url.replaceAll(staticDomain, publicDomain));
        sysFileInfo.setCreateTime(new Date());
        sysFileInfo.setReferType(referType);
        sysFileInfo.setReferId(referId);
        this.save(sysFileInfo);
        return sysFileInfo;
    }

    @Override
    public void bindBuss(List<SysFileInfo> list, String referId) {
        if (list != null && !list.isEmpty()) {
            for (SysFileInfo sysFileInfo : list) {
                sysFileInfo.setReferId(referId);
            }
            this.saveOrUpdateBatch(list);
        }
    }


    @Override
    public void screenShotVideo(MultipartFile file, String videoId) {
        String filename = file.getOriginalFilename();
        log.info("{}开始截屏", filename);
        try {
            if (ObjectUtil.isNotEmpty(filename)) {
                String fileSuffix = StrUtil.subAfter(filename, SymbolConstant.PERIOD, true);
                String lowerCase = fileSuffix.toLowerCase();
                if ("mp4".equals(lowerCase)) {
                    log.info("捕捉到mp4");
                    //截图
                    MultipartFile multipartFile = VideoUtil.getScreenshot(file);
                    //保存截图
                    SysFileInfo sysFileInfo = this.uploadFileOss(multipartFile, "screenshot", "");
                    //更新视频信息中的截图
                    this.update(new UpdateWrapper<SysFileInfo>().lambda().eq(SysFileInfo::getId, videoId).set(SysFileInfo::getScreenshotUrl, sysFileInfo.getOutPath()));
                    log.info("更新成功");
                }
            }

        } catch (Exception e) {
            System.out.println("截取视频图片失败：" + filename);
            e.printStackTrace();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdateDescription() {
        List<SysFileInfo> list = this.list(new QueryWrapper<SysFileInfo>().lambda().isNull(SysFileInfo::getDescription));
        if (list != null && !list.isEmpty()) {
            for (SysFileInfo sysFileInfo : list) {
                //文件名称去除后缀
                String fileOriginName = sysFileInfo.getFileOriginName();
                String substring = fileOriginName.substring(0, fileOriginName.lastIndexOf("."));
                sysFileInfo.setDescription(substring);
            }
            this.updateBatchById(list);
        }
        
    }
}
