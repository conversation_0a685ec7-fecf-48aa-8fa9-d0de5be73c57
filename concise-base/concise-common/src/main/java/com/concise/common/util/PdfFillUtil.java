package com.concise.common.util;

import cn.hutool.core.util.ObjectUtil;
import com.concise.common.pojo.vo.PdfModalVo;
import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.PdfTextFormField;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.annot.PdfWidgetAnnotation;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.poi.util.IOUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/18
 * 填充pdf模版工具类
 */
public class PdfFillUtil {

    /**
     * 填充模版
     *
     * @param path           模版的文件路径
     * @param pdfModalVoList 需要填充的内容信息
     * @return
     */
    public static String fillByTemplate(String path, List<PdfModalVo> pdfModalVoList) {
        try {
            // 读取本地 PDF 模板文件
            PdfReader reader = new PdfReader(Files.newInputStream(new File(path).toPath()));

            // 创建一个输出流来保存填充后的 PDF 数据
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 创建一个 PDF 文档，并将输出流与其关联
            PdfDocument pdfDocument = new PdfDocument(reader, new PdfWriter(outputStream));


            // 获取 PDF 表单
            PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDocument, true);
            pdfModalVoList.forEach((item -> {
                PdfTextFormField nameField = (PdfTextFormField) form.getField(item.getName());
                if (ObjectUtil.isNotNull(nameField)) {
                    //设置字体
                    PdfFont font = null;
                    try {
                        InputStream fontInputStream = Files.newInputStream(Paths.get(item.getFontPath()));
                        byte[] fontBytes = IOUtils.toByteArray(fontInputStream);
                        // 创建字体
                        font = PdfFontFactory.createFont(fontBytes, item.getEncoding(), true);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    nameField.setFont(font).setFontSize(item.getFontSize());
                    nameField.setValue(item.getValue());
                    //如果填充的时候需要下划线
                    if (item.isUnderline()) {
                        // 获取字段的坐标和大小
                        for (PdfWidgetAnnotation widgetAnnotation : nameField.getWidgets()) {
                            float x = widgetAnnotation.getRectangle().toRectangle().getX();
                            float y = widgetAnnotation.getRectangle().toRectangle().getBottom();
                            float width = widgetAnnotation.getRectangle().toRectangle().getWidth();
                            // 在字段下面绘制下划线
                            PdfPage page = widgetAnnotation.getPage();

                            PdfCanvas canvas = new PdfCanvas(page);
                            // 获取文本宽度
                            float textWidth = (float) (font.getWidth(item.getValue()) * item.getFontSize()) / 1000; // 16 是字体大小
                            // 计算下划线的长度
                            float underlineLength = Math.min(width, textWidth);

                            // 在字段下面绘制下划线
                            float lineWidth = 1f;
                            canvas.setLineWidth(lineWidth);
                            canvas.moveTo(x, y - lineWidth / 2);
                            canvas.lineTo(x + underlineLength, y - lineWidth / 2);
                            canvas.stroke();
                        }
                    }

                }
            }));

            // 关闭 PDF 文档
            pdfDocument.close();

            // 将填充后的 PDF 数据写入字节数组
            byte[] pdfData = outputStream.toByteArray();
            //先转图片再转回pdf，去除模版的填充效果
            BufferedImage bufferedImage = convertPdfToImage(pdfData);
            byte[] bytes = convertImageToPdf(bufferedImage);
            // 将转换好的 PDF 数据进行 Base64 编码
            String base64Data = Base64.getEncoder().encodeToString(bytes);
            return base64Data;

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }

    }

    // 将PDF字节数组转为图片
    private static BufferedImage convertPdfToImage(byte[] pdfByteArray) throws IOException {
        PDDocument document = PDDocument.load(new ByteArrayInputStream(pdfByteArray));
        PDFRenderer renderer = new PDFRenderer(document);
        BufferedImage image = renderer.renderImageWithDPI(0, 300);
        document.close();
        return image;
    }

    // 将图片转为PDF
    private static byte[] convertImageToPdf(BufferedImage image) throws IOException {
        PDDocument document = new PDDocument();
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);

        PDImageXObject pdImage = LosslessFactory.createFromImage(document, image);

        PDPageContentStream contentStream = new PDPageContentStream(document, page);
        contentStream.drawImage(pdImage, 0, 0, PDRectangle.A4.getWidth(), PDRectangle.A4.getHeight());
        contentStream.close();

        ByteArrayOutputStream newPdfByteArray = new ByteArrayOutputStream();
        document.save(newPdfByteArray);
        document.close();

        return newPdfByteArray.toByteArray();
    }


    /**
     * 用于公安再犯协同转换数据，引用到其他项目可删
     *
     * @param base64String
     * @param fileName
     * @return
     */


    public static MultipartFile convertBase64ToMultipartFile(String base64String, String fileName) throws IOException {
        // 解码Base64字符串为字节数组
        byte[] decodedBytes = Base64.getDecoder().decode(base64String);
        // 将文件转为MultipartFile
        return new MockMultipartFile(fileName, fileName, "pdf", decodedBytes);
    }

    public static void main(String[] args) {
//        String fillByTemplate = fillByTemplate("/Users/<USER>/Downloads/template/模版.pdf", putList("/Users/<USER>/Downloads/template/仿宋_GB2312.ttf"));
//        String signedBase64 = ElectronicSignatureUtil.signedBase64(fillByTemplate, "33012308031137144322", "社区矫正测试.pdf", "1", "400", "350");
//        System.out.println(fillByTemplate);
//        System.out.println(signedBase64);
    }
}
