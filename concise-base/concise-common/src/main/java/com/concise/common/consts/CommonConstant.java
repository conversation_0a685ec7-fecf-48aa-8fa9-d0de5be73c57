package com.concise.common.consts;

import cn.hutool.core.collection.CollectionUtil;

import java.util.List;

/**
 * 通用常量
 *
 * <AUTHOR>
 * @date 2020/3/11 16:51
 */
public interface CommonConstant {

    /**
     * id
     */
    String ID = "id";

    /**
     * 名称
     */
    String NAME = "name";

    /**
     * 编码
     */
    String CODE = "code";

    /**
     * 值
     */
    String VALUE = "value";

    /**
     * 默认标识状态的字段名称
     */
    String STATUS = "status";

    /**
     * 默认逻辑删除的状态值
     */
    String DEFAULT_LOGIC_DELETE_VALUE = "2";

    /**
     * 用户代理
     */
    String USER_AGENT = "User-Agent";

    /**
     * 请求头token表示
     */
    String AUTHORIZATION = "Authorization";

    /**
     * token名称
     */
    String TOKEN_NAME = "token";

    /**
     * token类型
     */
    String TOKEN_TYPE_BEARER = "Bearer";

    /**
     * 首页提示语
     */
    String INDEX_TIPS = "Welcome To Concise";

    /**
     * 未知标识
     */
    String UNKNOWN = "Unknown";

    /**
     * 默认包名
     */
    String DEFAULT_PACKAGE_NAME = "com.concise";

    /**
     * 系统包名前缀
     */
    String SYSTEM_PACKAGE_NAME = "com.concise.sys";

    /**
     * 默认包名
     */
    String BUSINESS_PACKAGE_NAME = "com.concise.modular";

    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "ZLPF@2024";

    /**
     * 请求号在header中的唯一标识
     */
    String REQUEST_NO_HEADER_NAME = "Request-No";

    /**
     * 数据库链接URL标识
     */
    String DATABASE_URL_NAME = "DATABASE_URL_NAME";

    /**
     * 数据库链接驱动标识
     */
    String DATABASE_DRIVER_NAME = "DATABASE_DRIVER_NAME";

    /**
     * 数据库用户标识
     */
    String DATABASE_USER_NAME = "DATABASE_USER_NAME";

    /**
     * 点选验证码
     */
    String IMAGE_CODE_TYPE = "clickWord";

    /**
     * undefined未知
     */
    String UNDEFINED = "undefined";

    /**
     * 字典值-Y
     */
    String YES = "Y";

    /**
     * 字典值-N
     */
    String NO = "N";

    String USER_PAPER_PREFIX = "USER_PAPER";

    String PREFIX = "_";

    String PAPER_PREFIX = "EXAM_PAPER_";

    List<String> CITY_LIST = CollectionUtil.newArrayList("杭州市", "宁波市", "温州市", "嘉兴市","湖州市","绍兴市", "金华市", "衢州市", "舟山市", "台州市", "丽水市");

    String TOP_DUTY_ORG_ID = "DUTY_a9105f9d532c372198bab8cd1d57dd9e";

    String TOP_ORG_ID = "LD_d858587bd54945edb6232980ba1549f7";

    String TOP_AREA_CODE = "330000";

    String TOP_AREA_NAME = "浙江省";

    /**
     * 资源搜索缓存
     */
    String RESOURCE_SEARCH_CACHE = "RESOURCE_SEARCH_CACHE_";
    /**
     * 通知缓存
     */
    String NOTICE_CACHE = "NOTICE_CACHE_";

    /**
     * 观测点联络员角色
     */
    String GCDLLY = "gcdlly";

    /**
     * 省普法责任制单位角色
     */
    String SPFZRZDW = "spfzrzdw";

    /**
     * 市普治处业务审核人
     */
    String SPZCYWSHR = "spzcywshr";

    /**
     * 市普治处工作人员
     */
    String SPZCGZRY = "spzcgzry";

    /**
     * 区普治科工作人员
     */
    String QPZKGZRY = "qpzkgzry";

    /**
     * 司法所工作人员
     */
    String SFSGZRY = "sfsgzry";

    /**
     * 省普治处业务审核人员
     */
    String STPZCYWSHR = "stpzcywshr";

    /**
     * 运营管理-申报通知
     */
    String OPERATION_DECLARE_NOTICE = "OPERATION_DECLARE_NOTICE";

    /**
     * 运营管理-未运营通知
     */
    String OPERATION_NOT_OPERATE_NOTICE = "OPERATION_NOT_OPERATE_NOTICE";


    /**
     * 运营管理-低频运营通知
     */
    String OPERATION_LOW_FREQUENCY_NOTICE = "OPERATION_LOW_FREQUENCY_NOTICE";

    /**
     * 运营管理-排名通知
     */
    String OPERATION_RANK_NOTICE = "OPERATION_RANK_NOTICE";

    /**
     * 运营管理-接受任务通知
     */
    String OPERATION_TASK_NOTICE = "OPERATION_TASK_NOTICE";

    String PROVINCE_NAME = "浙江省";

    String TOP_DUTY_ORG_NAME = "浙江省政府";

    String DUTY_SYMBOL = "DUTY_";
}
