/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021-07-21 16:36:28
 * @Description 脑图转SQL工具类
 */
DROP TABLE IF EXISTS `legal_aid_cases`;
CREATE TABLE `legal_aid_cases` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '法律援助案件主键',
  `case_num` varchar(255) DEFAULT NULL COMMENT '案号',
  `dic_case_type` varchar(255) DEFAULT NULL COMMENT '案件类型 民事案件、刑事案件、行政案件',
  `case_detail` varchar(255) DEFAULT NULL COMMENT '案情概况',
  `dic_involve_count` varchar(255) DEFAULT NULL COMMENT '案件涉及多人 N：个人案件，M:群体性案件',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='法律援助案件';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `legal_help_people_temp`;
CREATE TABLE `legal_help_people_temp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '承办援助人与案件关联信息表主键',
  `t_case_id` bigint(20) DEFAULT NULL COMMENT '案件id',
  `t_org_id` bigint(20) DEFAULT NULL COMMENT '承办人机构id',
  `t_hp_user_id` bigint(20) DEFAULT NULL COMMENT '承办援助人id',
  `hp_work_unit` varchar(255) DEFAULT NULL COMMENT '承办援助人工作单位',
  `hp_name` varchar(255) DEFAULT NULL COMMENT '承办人（援助人员姓名）',
  `dic_hp_identity` varchar(255) DEFAULT NULL COMMENT '法律援助人员类型',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='承办援助人与案件关联信息表';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `legal_accept_people`;
CREATE TABLE `legal_accept_people` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '受援人与案件关联表主键',
  `t_case_id` bigint(20) DEFAULT NULL COMMENT '案件id',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `dic_card_type` varchar(255) DEFAULT NULL COMMENT '证件类型',
  `card_code` varchar(255) DEFAULT NULL COMMENT '证件号',
  `dic_gender` varchar(255) DEFAULT NULL COMMENT '性别',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='受援人与案件关联表';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `legal_evaluate`;
CREATE TABLE `legal_evaluate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '法律援助评价信息表主键',
  `t_case_id` bigint(20) DEFAULT NULL COMMENT '案件ID',
  `dic_summary` varchar(255) DEFAULT NULL COMMENT '受援人总体评价',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='法律援助评价信息表';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `t_orm_judge_opinion`;
CREATE TABLE `t_orm_judge_opinion` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '法官评价信息（t_orm_judge_opinion）主键',
  `t_case_id` bigint(20) DEFAULT NULL COMMENT '案件id 未评价',
  `dic_etiquette` varchar(255) DEFAULT NULL COMMENT '诉讼礼仪 诉讼礼仪：1、满意 2、基本满意 3、不满意',
  `dic_accomplishment` varchar(255) DEFAULT NULL COMMENT '专业素养 专业素养 ：1、满意 2、基本满意 3、不满意',
  `dic_dedication` varchar(255) DEFAULT NULL COMMENT '敬业精神 敬业精神： 1、满意 2、基本满意 3、不满意',
  `suggestion` varchar(255) DEFAULT NULL COMMENT '法官意见',
  `dic_status` varchar(255) DEFAULT NULL COMMENT '状态 状态：1、未评价 2、已评价',
  `dic_method` varchar(255) DEFAULT NULL COMMENT '评价方式 评价方式 1、自评  2、系统评',
  `dic_summary` varchar(255) DEFAULT NULL COMMENT '总体评价 总体评价 1、满意 2、基本满意 3、不满意',
  `is_evaluate` varchar(255) DEFAULT NULL COMMENT '是否能评价 是否能评价(0.能，1.不能）',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='法官评价信息（t_orm_judge_opinion）';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `sifa_case_sms_grade`;
CREATE TABLE `sifa_case_sms_grade` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '短信评价（sifa_case_sms_grade）主键',
  `caseid` bigint(20) DEFAULT NULL COMMENT '案件',
  `litigantid` bigint(20) DEFAULT NULL COMMENT '短信评价人id',
  `litigantname` varchar(255) DEFAULT NULL COMMENT '短信评价人',
  `grade` varchar(255) DEFAULT NULL COMMENT '评分等级 很不满意、不满意、一般、满意、非常满意',
  `ajbm` varchar(255) DEFAULT NULL COMMENT '案件编码',
  `ajly` varchar(255) DEFAULT NULL COMMENT '案件来源',
  `suggest` varchar(255) DEFAULT NULL COMMENT '建议',
  `messageid` bigint(20) DEFAULT NULL COMMENT '短信ID',
  `status` varchar(255) DEFAULT NULL COMMENT '评分状态 0-未评分 1-已评分 2-超时系统默认评分',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='短信评价（sifa_case_sms_grade）';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `legal_mediators`;
CREATE TABLE `legal_mediators` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '纠纷案件与调解员关联表主键',
  `caseid` bigint(20) DEFAULT NULL COMMENT '案件ID',
  `userid` bigint(20) DEFAULT NULL COMMENT '调解员ID',
  `type` varchar(255) DEFAULT NULL COMMENT '类型 1：主办调解员；0：协办调解员',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='纠纷案件与调解员关联表';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `legal_case`;
CREATE TABLE `legal_case` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '纠纷案件信息主键',
  `ajbm` varchar(255) DEFAULT NULL COMMENT '案件编码',
  `slrq` varchar(255) DEFAULT NULL COMMENT '受理时间',
  `jflb` varchar(255) DEFAULT NULL COMMENT '纠纷类别',
  `state` varchar(255) DEFAULT NULL COMMENT '纠纷阶段',
  `ajndjb` varchar(255) DEFAULT NULL COMMENT '案件难度级别',
  `JFZHQK` varchar(255) DEFAULT NULL COMMENT '纠纷转化情况',
  `AJSX` varchar(255) DEFAULT NULL COMMENT '案件属性',
  `tjsj` varchar(255) DEFAULT NULL COMMENT '调解时间',
  `tjjg` varchar(255) DEFAULT NULL COMMENT '调解结果',
  `xylxqk` varchar(255) DEFAULT NULL COMMENT '履行情况',
  `jaTime` varchar(255) DEFAULT NULL COMMENT '结案时间',
  `jfjyqk` varchar(255) DEFAULT NULL COMMENT '纠纷简要情况',
  `twhid` bigint(20) DEFAULT NULL COMMENT '调委会ID',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='纠纷案件信息';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `integral_set`;
CREATE TABLE `integral_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '积分设置主键',
  `key` varchar(255) DEFAULT NULL COMMENT '标志',
  `score` varchar(255) DEFAULT NULL COMMENT '分值',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='积分设置';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `integral_record`;
CREATE TABLE `integral_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '积分记录主键',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='积分记录';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `integral`;
CREATE TABLE `integral` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '积分主键',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='积分';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `declar_activities`;
CREATE TABLE `declar_activities` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申报活动主键',
  `declar_id` bigint(20) DEFAULT NULL COMMENT '申报ID',
  `activity_theme` varchar(255) DEFAULT NULL COMMENT '活动主题',
  `activity_remarks` varchar(255) DEFAULT NULL COMMENT '活动简介',
  `service_category` varchar(255) DEFAULT NULL COMMENT '服务种类',
  `service_items` varchar(255) DEFAULT NULL COMMENT '服务事项',
  `activitie_start_time` datetime DEFAULT NULL COMMENT '活动开始时间',
  `activity_end_time` datetime DEFAULT NULL COMMENT '活动开始时间',
  `service_personnel_type` varchar(255) DEFAULT NULL COMMENT '服务人员类型(,)间隔开',
  `release_status` varchar(255) DEFAULT NULL COMMENT '发布状态',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='申报活动';
SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `declar`;
CREATE TABLE `declar` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申报主键',
  `review_results` varchar(255) DEFAULT NULL COMMENT '审核结果',
  `audit_opinion` varchar(255) DEFAULT NULL COMMENT '审核意见',
  `result_id` bigint(20) DEFAULT NULL COMMENT '填报人ID',
  `service_category` varchar(255) DEFAULT NULL COMMENT '服务种类',
  `service_items` varchar(255) DEFAULT NULL COMMENT '服务事项',
  `remarks` varchar(255) DEFAULT NULL COMMENT '工作简介',
  `declare_att` varchar(255) DEFAULT NULL COMMENT '申报附件',
  `review_status` varchar(255) DEFAULT NULL COMMENT '审核状态',
  `create_user` varchar(255) NULL COMMENT '创建人',
  `update_user` varchar(255) NULL COMMENT '最后更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='申报';
SET FOREIGN_KEY_CHECKS = 1;

