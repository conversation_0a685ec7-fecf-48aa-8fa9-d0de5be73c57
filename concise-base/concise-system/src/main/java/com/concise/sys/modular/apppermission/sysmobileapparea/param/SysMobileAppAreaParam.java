package com.concise.sys.modular.apppermission.sysmobileapparea.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 移动端权限区域表参数类
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:09
*/
@Data
public class SysMobileAppAreaParam extends BaseParam {

    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @NotNull(message = "标识不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 应用编码
     */
    @ApiModelProperty(value = "应用编码")
    @NotBlank(message = "应用编码不能为空，请检查appCode参数", groups = {add.class, edit.class})
    private String appCode;

    /**
     * 区域id
     */
    @ApiModelProperty(value = "区域id")
    @NotBlank(message = "区域id不能为空，请检查areaId参数", groups = {add.class, edit.class})
    private String areaId;

}
