package com.concise.sys.modular.apppermission.sysmobileapp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.concise.common.pojo.base.entity.BaseEntity;

/**
 * 移动端权限
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:07
 */
@Data
@TableName("sys_mobile_app")
public class SysMobileApp {

    /**
     * 应用编码
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String appCode;

    /**
     * 应用名称
     */
    private String appName;

}
