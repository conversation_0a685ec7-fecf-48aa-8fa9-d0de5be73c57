package com.concise.sys.modular.area.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.concise.sys.modular.area.entity.SysArea;
import com.concise.sys.modular.area.mapper.SysAreaMapper;
import com.concise.sys.modular.area.param.SysAreaParam;
import com.concise.sys.modular.area.service.SysAreaService;

import java.util.List;

/**
 * 系统区域service接口实现类
 *
 * <AUTHOR>
 * @date 2020/3/13 16:11
 */
@Service
public class SysAreaServiceImpl extends ServiceImpl<SysAreaMapper, SysArea> implements SysAreaService {

    @Override
    public List<SysArea> list(SysAreaParam sysAreaParam) {
        LambdaQueryWrapper<SysArea> queryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotNull(sysAreaParam)) {
            if(ObjectUtil.isNotEmpty(sysAreaParam.getParentCode())) {
                queryWrapper.eq(SysArea::getParentCode, sysAreaParam.getParentCode());
            } else {
                queryWrapper.eq(SysArea::getParentCode, "0");
            }
        }
        return this.list(queryWrapper);
    }
}
