package com.concise.sys.modular.apppermission.sysmobileapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.node.AntdBaseTreeNode;
import com.concise.common.pojo.page.PageResult;
import com.concise.sys.modular.apppermission.sysmobileapp.entity.SysMobileApp;
import com.concise.sys.modular.apppermission.sysmobileapp.param.SysMobileAppParam;

import java.util.List;

/**
 * 移动端权限service接口
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:07
 */
public interface SysMobileAppService extends IService<SysMobileApp> {

    /**
     * 查询移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    PageResult<SysMobileApp> page(SysMobileAppParam sysMobileAppParam);

    /**
     * 移动端权限列表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    List<SysMobileApp> list(SysMobileAppParam sysMobileAppParam);

    /**
     * 添加移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    void add(SysMobileAppParam sysMobileAppParam);

    /**
     * 删除移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    void delete(SysMobileAppParam sysMobileAppParam);

    /**
     * 编辑移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    void edit(SysMobileAppParam sysMobileAppParam);

    /**
     * 查看移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    SysMobileApp detail(SysMobileAppParam sysMobileAppParam);

    /**
     * 根据区域编码获取移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    List<String> getAppIdsByAreaCode(String areaCode);

    /**
     * 授权
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    void auth(SysMobileAppParam sysMobileAppParam);

    /**
     * 获取已授权的区域
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    List<String> getAreaCodes(SysMobileAppParam sysMobileAppParam);

}
