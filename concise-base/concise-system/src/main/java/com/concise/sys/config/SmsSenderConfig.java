package com.concise.sys.config;

import cn.hutool.core.bean.BeanUtil;
import com.concise.common.context.constant.ConstantContextHolder;
import com.concise.common.pojo.sms.AliyunSmsConfigs;
import com.concise.core.sms.modular.aliyun.msign.impl.MapBasedMultiSignManager;
import com.concise.core.sms.modular.aliyun.prop.AliyunSmsProperties;
import com.concise.core.sms.SmsSender;
import com.concise.core.sms.modular.aliyun.AliyunSmsSender;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 短信发送配置，短信发送的配置属性都在数据库的sys_config表中
 * <p>
 * 默认开启了阿里云的短信配置
 *
 * <AUTHOR>
 * @date 2020/6/6 22:27
 */
@Configuration
public class SmsSenderConfig {

    /**
     * 短信发送器（阿里云）
     *
     * <AUTHOR>
     * @date 2020/6/6 22:30
     */
    @Bean
    public SmsSender aliyunSmsSender() {

        // 从数据库配置读取阿里云配置
        AliyunSmsConfigs aliyunSmsConfigs = ConstantContextHolder.getAliyunSmsConfigs();
        AliyunSmsProperties aliyunSmsProperties = new AliyunSmsProperties();
        BeanUtil.copyProperties(aliyunSmsConfigs, aliyunSmsProperties);

        return new AliyunSmsSender(new MapBasedMultiSignManager(), aliyunSmsProperties);
    }

}
