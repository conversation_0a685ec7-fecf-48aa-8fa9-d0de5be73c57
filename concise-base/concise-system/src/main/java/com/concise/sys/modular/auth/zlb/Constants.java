package com.concise.sys.modular.auth.zlb;

/**
 * <AUTHOR>
 * @date 2022-03-30 15:24
 */
public interface Constants {

    /**
     * 单点登录 ticketId换token的地址
     */
    String ACCESS_TOKEN_URL = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220329000007/uc/sso/access_token";
    /**
     * 单点登录 token获取用户信息地址
     */
    String GET_USER_INFO_URL = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220329000008/uc/sso/getUserInfo";

    /**
     * IRS请求携带的请求头
     */
    String X_BG_HMAC_ACCESS_KEY = "X-BG-HMAC-ACCESS-KEY";
    String X_BG_HMAC_SIGNATURE = "X-BG-HMAC-SIGNATURE";
    String X_BG_HMAC_ALGORITHM = "X-BG-HMAC-ALGORITHM";
    String X_BG_DATE_TIME = "X-BG-DATE-TIME";

    /**
     * IRS签名算法
     */
    String DEFAULT_HMAC_SIGNATURE = "hmac-sha256";

    /**
     * 应用ID
     */
    String APP_ID = "2002281766";
    /**
     * 微信端固定值为weixin
     */
    String WEIXIN_ENDPOINT_TYPE = "weixin";


    /**
     * IRS 申请组件生成的AK
     */
    String IRS_AK = "168cfe14267e4fb491fff8b842e71ee8";
    /**
     * IRS 申请组件生成的SK
     */
    String IRS_SK = "8159e7350a1d44b29f0a68c63350c9a8";


    String TOKEN_SESSION_KEY = "sessionAccessToken";
    String USER_INFO_KEY = "sessionUserInfo";

}
