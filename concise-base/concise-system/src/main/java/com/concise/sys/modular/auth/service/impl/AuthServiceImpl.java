package com.concise.sys.modular.auth.service.impl;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.TimerTask;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ScheduledExecutorFactoryBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.concise.common.consts.CommonConstant;
import com.concise.common.context.constant.ConstantContextHolder;
import com.concise.common.enums.CommonStatusEnum;
import com.concise.common.exception.AuthException;
import com.concise.common.exception.ServiceException;
import com.concise.common.exception.enums.AuthExceptionEnum;
import com.concise.common.exception.enums.ServerExceptionEnum;
import com.concise.common.util.HttpServletUtil;
import com.concise.common.util.IpAddressUtil;
import com.concise.core.context.login.LoginContext;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.dbs.CurrentDataSourceContext;
import com.concise.core.login.SysLoginUser;
import com.concise.core.tenant.context.TenantCodeHolder;
import com.concise.core.tenant.context.TenantDbNameHolder;
import com.concise.core.tenant.entity.TenantInfo;
import com.concise.core.tenant.exception.TenantException;
import com.concise.core.tenant.exception.enums.TenantExceptionEnum;
import com.concise.core.tenant.service.TenantInfoService;
import com.concise.sys.core.cache.UserCache;
import com.concise.sys.core.enums.LogSuccessStatusEnum;
import com.concise.sys.core.jwt.JwtPayLoad;
import com.concise.sys.core.jwt.JwtTokenUtil;
import com.concise.sys.core.log.LogManager;
import com.concise.sys.modular.auth.factory.LoginUserFactory;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 认证相关service实现类
 *
 * <AUTHOR>
 * @date 2020/3/11 16:58
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService, UserDetailsService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private UserCache userCache;

    public static final String PASSWORD_FAIL = "password_fail_";

    public static final SysUserService userService = SpringUtil.getBean(SysUserService.class);

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private static final ScheduledThreadPoolExecutor EXECUTOR = new ScheduledThreadPoolExecutor(10, new ScheduledExecutorFactoryBean());


    @Override
    public String login(String account, String password, String type) {

        if (ObjectUtil.hasEmpty(account, password)) {
            if (ObjectUtil.isNotEmpty(type) && "zlb".equals(type)) {
                LogManager.me().executeZlbLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_EMPTY.getMessage());
            } else {
                LogManager.me().executeLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_EMPTY.getMessage());
            }
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_EMPTY);
        }

        SysUser sysUser = sysUserService.getUserByCount(account);

        //用户不存在，账号或密码错误
        if (ObjectUtil.isEmpty(sysUser)) {
            if (ObjectUtil.isNotEmpty(type) && "zlb".equals(type)) {
                LogManager.me().executeZlbLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
            } else {
                LogManager.me().executeLoginLog(account, LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_PWD_ERROR.getMessage());
            }
            throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
        } else {
            String passwordBcrypt = sysUser.getPassword();
            String key = PASSWORD_FAIL + sysUser.getId();
            Object number = stringRedisTemplate.opsForValue().get(key);

            if (ObjectUtil.isNotNull(number)) {
                int i = Integer.parseInt(number.toString()) + 1;
                //判断是否有冻结记录，有的话直接不给登录
                if (i >= 6) {
                    throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
                }
                //没有大于5次，但是也有输错记录，这次也输错了，则加输错次数
                if (ObjectUtil.isEmpty(passwordBcrypt) || !DigestUtil.md5Hex(password).equals(passwordBcrypt)) {
                    stringRedisTemplate.opsForValue().set(key, String.valueOf(i), 5, TimeUnit.MINUTES);
                    throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
                }

            }
            //首次输错，直接加1
            if (ObjectUtil.isEmpty(passwordBcrypt) || !DigestUtil.md5Hex(password).equals(passwordBcrypt)) {
                stringRedisTemplate.opsForValue().set(key, "1", 5, TimeUnit.MINUTES);
                throw new AuthException(AuthExceptionEnum.ACCOUNT_PWD_ERROR);
            }
            //输入正确，前面的次数清零
            stringRedisTemplate.delete(key);
        }
        if (ObjectUtil.isNotEmpty(type) && "zlb".equals(type)) {
            return doLoginZlb(sysUser, type);
        }

        return doLogin(sysUser);
    }

    @Override
    public String doLoginZlb(SysUser sysUser, String type) {
        Integer sysUserStatus = sysUser.getStatus();

        //验证账号是否被冻结
        if (CommonStatusEnum.DISABLE.getCode().equals(sysUserStatus)) {
            LogManager.me().executeZlbLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_FREEZE_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
        }

        //构造SysLoginUser
        SysLoginUser sysLoginUser = this.genSysLoginUser(sysUser, null);

        //构造jwtPayLoad
        JwtPayLoad jwtPayLoad = new JwtPayLoad(sysUser.getId(), sysUser.getAccount());

        //生成token
        String token = JwtTokenUtil.generateToken(jwtPayLoad);

        //缓存token与登录用户信息对应, 默认2个小时
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        //设置最后登录ip和时间
        sysUser.setLastLoginIp(IpAddressUtil.getIp(HttpServletUtil.getRequest()));
        sysUser.setLastLoginTime(DateTime.now());
        if (ObjectUtil.isEmpty(sysUser.getFirstLoginTime())) {
            sysUser.setFirstLoginTime(DateUtil.date());
        }
        //更新用户登录信息
        sysUserService.updateById(sysUser);

        //登录成功，记录登录日志
        LogManager.me().executeZlbLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.SUCCESS.getCode(), null);

        //登录成功，设置SpringSecurityContext上下文，方便获取用户
        this.setSpringSecurityContextAuthentication(sysLoginUser);

        //如果开启限制单用户登陆，则踢掉原来的用户
        Boolean enableSingleLogin = ConstantContextHolder.getEnableSingleLogin();
        if (enableSingleLogin) {

            //获取所有的登陆用户
            Map<String, SysLoginUser> allLoginUsers = userCache.getAllKeyValues();
            for (Map.Entry<String, SysLoginUser> loginedUserEntry : allLoginUsers.entrySet()) {

                String loginedUserKey = loginedUserEntry.getKey();
                SysLoginUser loginedUser = loginedUserEntry.getValue();

                //如果账号名称相同，并且redis缓存key和刚刚生成的用户的uuid不一样，则清除以前登录的
                if (loginedUser.getName().equals(sysUser.getName())
                        && !loginedUserKey.equals(jwtPayLoad.getUuid())) {
                    this.clearUser(loginedUserKey, loginedUser.getAccount());
                }
            }
        }

        //返回token
        return token;
    }

    @Override
    public String doLogin(SysUser sysUser) {

        Integer sysUserStatus = sysUser.getStatus();

        //验证账号是否被冻结
        if (CommonStatusEnum.DISABLE.getCode().equals(sysUserStatus)) {
            LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.FAIL.getCode(), AuthExceptionEnum.ACCOUNT_FREEZE_ERROR.getMessage());
            throw new AuthException(AuthExceptionEnum.ACCOUNT_FREEZE_ERROR);
        }

        //构造SysLoginUser
        SysLoginUser sysLoginUser = this.genSysLoginUser(sysUser, null);

        //构造jwtPayLoad
        JwtPayLoad jwtPayLoad = new JwtPayLoad(sysUser.getId(), sysUser.getAccount());

        //生成token
        String token = JwtTokenUtil.generateToken(jwtPayLoad);

        //缓存token与登录用户信息对应, 默认2个小时
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        //设置最后登录ip和时间
        String ip = IpAddressUtil.getIp(HttpServletUtil.getRequest());
        sysUser.setLastLoginIp(ip);
//        //异步处理ip转中文再转code
//        saveCity(sysUser, ip);
        sysUser.setLastLoginTime(DateTime.now());
        if (ObjectUtil.isEmpty(sysUser.getFirstLoginTime())) {
            sysUser.setFirstLoginTime(DateUtil.date());
        }
        //更新用户登录信息
        sysUserService.updateById(sysUser);

        //登录成功，记录登录日志
        LogManager.me().executeLoginLog(sysUser.getAccount(), LogSuccessStatusEnum.SUCCESS.getCode(), null);

        //登录成功，设置SpringSecurityContext上下文，方便获取用户
        this.setSpringSecurityContextAuthentication(sysLoginUser);

        //如果开启限制单用户登陆，则踢掉原来的用户
        Boolean enableSingleLogin = ConstantContextHolder.getEnableSingleLogin();
        if (enableSingleLogin) {

            //获取所有的登陆用户
            Map<String, SysLoginUser> allLoginUsers = userCache.getAllKeyValues();
            for (Map.Entry<String, SysLoginUser> loginedUserEntry : allLoginUsers.entrySet()) {

                String loginedUserKey = loginedUserEntry.getKey();
                SysLoginUser loginedUser = loginedUserEntry.getValue();

                //如果账号名称相同，并且redis缓存key和刚刚生成的用户的uuid不一样，则清除以前登录的
                if (loginedUser.getName().equals(sysUser.getName())
                        && !loginedUserKey.equals(jwtPayLoad.getUuid())) {
                    this.clearUser(loginedUserKey, loginedUser.getAccount());
                }
            }
        }

        //返回token
        return token;
    }

    @Override
    public String getTokenFromRequest(HttpServletRequest request) {
        String authToken = request.getHeader(CommonConstant.AUTHORIZATION);
        if (ObjectUtil.isEmpty(authToken) || CommonConstant.UNDEFINED.equals(authToken)) {
            return null;
        } else {
            //token不是以Bearer打头，则响应回格式不正确
            if (!authToken.startsWith(CommonConstant.TOKEN_TYPE_BEARER)) {
                throw new AuthException(AuthExceptionEnum.NOT_VALID_TOKEN_TYPE);
            }
            try {
                authToken = authToken.substring(CommonConstant.TOKEN_TYPE_BEARER.length() + 1);
            } catch (StringIndexOutOfBoundsException e) {
                throw new AuthException(AuthExceptionEnum.NOT_VALID_TOKEN_TYPE);
            }
        }
        return authToken;
    }

    @Override
    public SysLoginUser getLoginUserByToken(String token) {

        //校验token，错误则抛异常
        this.checkToken(token);

        //根据token获取JwtPayLoad部分
        JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);

        //从redis缓存中获取登录用户
        Object cacheObject = userCache.get(jwtPayLoad.getUuid());

        //用户不存在则表示登录已过期
        if (ObjectUtil.isEmpty(cacheObject)) {
            throw new AuthException(AuthExceptionEnum.LOGIN_EXPIRED);
        }

        //转换成登录用户
        SysLoginUser sysLoginUser = (SysLoginUser) cacheObject;

        //用户存在, 无痛刷新缓存，在登录过期前活动的用户自动刷新缓存时间
        this.cacheLoginUser(jwtPayLoad, sysLoginUser);

        //返回用户
        return sysLoginUser;
    }

    @Override
    public void logout() {

        HttpServletRequest request = HttpServletUtil.getRequest();

        if (ObjectUtil.isNotNull(request)) {

            //获取token
            String token = this.getTokenFromRequest(request);

            //如果token为空直接返回
            if (ObjectUtil.isEmpty(token)) {
                return;
            }

            //校验token，错误则抛异常，待确定
            this.checkToken(token);

            //根据token获取JwtPayLoad部分
            JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);

            //获取缓存的key
            String loginUserCacheKey = jwtPayLoad.getUuid();
            this.clearUser(loginUserCacheKey, jwtPayLoad.getAccount());

        } else {
            throw new ServiceException(ServerExceptionEnum.REQUEST_EMPTY);
        }
    }

    @Override
    public void setSpringSecurityContextAuthentication(SysLoginUser sysLoginUser) {
        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken =
                new UsernamePasswordAuthenticationToken(
                        sysLoginUser,
                        null,
                        sysLoginUser.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
    }

    @Override
    public Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    @Override
    public void checkToken(String token) {
        //校验token是否正确
        Boolean tokenCorrect = JwtTokenUtil.checkToken(token);
        if (!tokenCorrect) {
            throw new AuthException(AuthExceptionEnum.REQUEST_TOKEN_ERROR);
        }

        //校验token是否失效
        Boolean tokenExpired = JwtTokenUtil.isTokenExpired(token);
        if (tokenExpired) {
            throw new AuthException(AuthExceptionEnum.LOGIN_EXPIRED);
        }
    }

    @Override
    public void cacheTenantInfo(String tenantCode) {
        if (StrUtil.isBlank(tenantCode)) {
            return;
        }

        // 从spring容器中获取service，如果没开多租户功能，没引入相关包，这里会报错
        TenantInfoService tenantInfoService = null;
        try {
            tenantInfoService = SpringUtil.getBean(TenantInfoService.class);
        } catch (Exception e) {
            throw new TenantException(TenantExceptionEnum.TENANT_MODULE_NOT_ENABLE_ERROR);
        }

        // 获取租户信息
        TenantInfo tenantInfo = tenantInfoService.getByCode(tenantCode);
        if (tenantInfo != null) {
            String dbName = tenantInfo.getDbName();

            // 租户编码的临时存放
            TenantCodeHolder.put(tenantCode);

            // 租户的数据库名称临时缓存
            TenantDbNameHolder.put(dbName);

            // 数据源信息临时缓存
            CurrentDataSourceContext.setDataSourceType(dbName);
        } else {
            throw new TenantException(TenantExceptionEnum.CNAT_FIND_TENANT_ERROR);
        }
    }

    @Override
    public SysLoginUser loadUserByUsername(String account) throws UsernameNotFoundException {
        SysLoginUser sysLoginUser = new SysLoginUser();
        SysUser user = sysUserService.getUserByCount(account);
        BeanUtil.copyProperties(user, sysLoginUser);
        return sysLoginUser;
    }

    /**
     * 根据key清空登陆信息
     *
     * <AUTHOR>
     * @date 2020/6/19 12:28
     */
    private void clearUser(String loginUserKey, String account) {
        //获取缓存的用户
        Object cacheObject = userCache.get(loginUserKey);

        //如果缓存的用户存在，清除会话，否则表示该会话信息已失效，不执行任何操作
        if (ObjectUtil.isNotEmpty(cacheObject)) {
            //清除登录会话
            userCache.remove(loginUserKey);
            //创建退出登录日志
            LogManager.me().executeExitLog(account);
        }
    }

    /**
     * 构造登录用户信息
     *
     * <AUTHOR>
     * @date 2020/3/12 17:32
     */
    @Override
    public SysLoginUser genSysLoginUser(SysUser sysUser, String orgId) {
        SysLoginUser sysLoginUser = new SysLoginUser();
        BeanUtil.copyProperties(sysUser, sysLoginUser);
        LoginUserFactory.fillLoginUserInfo(sysLoginUser, orgId);
        return sysLoginUser;
    }

    @Autowired
    private SysOrgService sysOrgService;

    @Override
    public Set<String> getLoginUserDataScopeIdList(String id) {
        LoginContext login = LoginContextHolder.me();
        String sysLoginUserOrgId = login.getSysLoginUserOrgId();
        String orgId = "";
        SysOrg sysOrg = sysOrgService.getById(sysLoginUserOrgId);
        if (null != sysOrg) {
            //判断登录人机构是否是部门，如果是部门则需要再往上找一级(参照信息化平台 type = sp 表示该组织机构是部门)
            if ("sp".equals(sysOrg.getType())) {
                orgId = sysOrg.getPid();
            }
        }
        Set<String> orgs = null;
        if (ObjectUtil.isNotEmpty(orgId)) {
            orgs = new HashSet<>();
            // 查询本级&下级
            sysOrgService.getSubOrgIdById(orgId, orgs, sysOrgService.list());
            orgs.add(orgId);
        }
        return orgs;
    }

    /**
     * 缓存token与登录用户信息对应, 默认2个小时
     *
     * <AUTHOR>
     * @date 2020/3/13 14:51
     */
    private void cacheLoginUser(JwtPayLoad jwtPayLoad, SysLoginUser sysLoginUser) {
        String redisLoginUserKey = jwtPayLoad.getUuid();
        userCache.put(redisLoginUserKey, sysLoginUser, Convert.toLong(ConstantContextHolder.getSessionTokenExpireSec()));
    }

    public void saveCity(SysUser sysUser, String ip) {
        EXECUTOR.schedule(updateSysUser(sysUser, ip), 20, TimeUnit.MILLISECONDS);
    }

    public static TimerTask updateSysUser(SysUser sysUser, String ip) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    log.debug("用户{}登录的ip:{}", sysUser.getName(), ip);
                    SysUser user = userService.getById(sysUser.getId());
                    if (ObjectUtil.isEmpty(user.getCityCode())) {
                        String cityName = IpAddressUtil.getCityNameByIP(ip);
                        log.debug("根据ip转换城市信息：{}", cityName);
                        if (ObjectUtil.isNotEmpty(cityName)) {
                            String cityCode = userService.getAreaCodeByAreaName(cityName);
                            user.setCityCode(cityCode);
                        }
                    }
                    if (ObjectUtil.isEmpty(user.getAreaCode())) {
                        String areaName = IpAddressUtil.getAreaNameByIP(ip);
                        log.debug("根据ip转换城市信息：{}", areaName);
                        if (ObjectUtil.isNotEmpty(areaName)) {
                            String areaCode = userService.getAreaCodeByAreaName(areaName);
                            user.setAreaCode(areaCode);
                        }
                    }
                    userService.updateById(user);
                } catch (Exception e) {
                    log.error("根据ip转换城市信息失败，姓名：{}，错误信息：{}", sysUser.getName(), e.getMessage());
                }
            }
        };
    }
}
