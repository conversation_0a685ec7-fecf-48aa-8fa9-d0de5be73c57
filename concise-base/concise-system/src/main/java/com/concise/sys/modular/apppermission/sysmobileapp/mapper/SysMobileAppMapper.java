package com.concise.sys.modular.apppermission.sysmobileapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.sys.modular.apppermission.sysmobileapp.entity.SysMobileApp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 移动端权限
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:07
 */
public interface SysMobileAppMapper extends BaseMapper<SysMobileApp> {

    /**
     * 获取移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    List<String> getAppIdsByAreaCode(@Param("areaCode") String areaCode);
}
