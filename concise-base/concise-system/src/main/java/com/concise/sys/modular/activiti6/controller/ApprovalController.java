package com.concise.sys.modular.activiti6.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.sys.modular.activiti6.service.ApprovalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipInputStream;

/**
 * <h1>
 * ApprovalController
 * 工作流相关控制器
 * </h1>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2023/03/22 15:42
 */
@Slf4j
@Api(tags = "工作流相关API")
@RestController
@CrossOrigin
@RequestMapping("/approval")
public class ApprovalController {

    @Autowired
    private ApprovalService approvalService;

    @Resource
    RepositoryService repositoryService;

    @ApiOperation(value = "获取部署流程列表", notes = "获取部署流程列表")
    @PostMapping("/processList")
    public ResponseData processList() {
        return new SuccessResponseData(approvalService.processList());
    }


    @ApiOperation(value = "部署流程", notes = "部署流程")
    @PostMapping("/deployments")
    public Map<String, Object> deployments(@RequestParam(value = "file", required = false) MultipartFile file) {
        try {
            String filename = file.getOriginalFilename();
            InputStream is = file.getInputStream();
            if (filename.endsWith("zip")) {
                repositoryService.createDeployment().name(filename).addZipInputStream(new ZipInputStream(is)).deploy();
            } else if (filename.endsWith("bpmn") || filename.endsWith("xml")) {
                repositoryService.createDeployment().name(filename).addInputStream(filename, is).deploy();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", 200);
        resultMap.put("success", true);
        resultMap.put("massage", "提交成功");
        return resultMap;
    }

    @ApiOperation(value = "删除流程", notes = "删除流程")
    @DeleteMapping("/deleteDeployments/{id}")
    public Map<String, Object> deleteDeployments(@PathVariable String id) {
        repositoryService.deleteDeployment(id, true);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", 200);
        resultMap.put("success", true);
        resultMap.put("massage", "提交成功");
        return resultMap;
    }

    /**
     * 构建返回响应体, 用于将task-service 返回值 转换为 后台所以需要的格式
     *
     * @param body
     * @return
     */
    @NotNull
    public Map<String, Object> buildResultMap(String body) {
        Map<String, Object> resultMap = new HashMap<>();
        JSONObject jsonObject = JSON.parseObject(body);

        if (ObjectUtil.isNotNull(jsonObject)) {
            if (ObjectUtil.isNull(jsonObject.get("code"))) {
                resultMap.put("code", 200);
                resultMap.put("success", true);
                resultMap.put("massage", "提交成功");
                resultMap.put("data", jsonObject);
            } else {
                resultMap.put("code", jsonObject.get("code"));
                resultMap.put("massage", jsonObject.get("msg"));
                resultMap.put("data", jsonObject.get("data"));
            }
        } else {
            throw new RuntimeException("不存在的数据");
        }
        return resultMap;
    }
}
