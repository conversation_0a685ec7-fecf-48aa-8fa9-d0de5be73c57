package com.concise.sys.core.filter.security.entrypoint;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.log.Log;
import com.concise.common.exception.ServiceException;
import com.concise.common.exception.enums.AuthExceptionEnum;
import com.concise.common.exception.enums.ServerExceptionEnum;
import com.concise.common.util.ResponseUtil;
import com.concise.sys.core.cache.ResourceCache;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.Collection;

import static com.concise.common.consts.CommonConstant.AUTHORIZATION;

/**
 * 未认证用户访问须授权资源端点
 *
 * <AUTHOR>
 * @date 2020/3/18 11:52
 */
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint, Serializable {

    private static final Log log = Log.get();

    @Resource
    private ResourceCache resourceCache;

    /**
     * 访问未经授权的接口时执行此方法，未经授权的接口包含系统中存在和不存在的接口，分别处理
     *
     * <AUTHOR>
     * @date 2020/3/18 19:15
     */
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) throws IOException {
        String requestUri = request.getRequestURI();
        //
        //1.检查redis中RESOURCE缓存是否为空，如果为空，直接抛出系统异常，缓存url作用详见ResourceCollectListener
        Collection<String> urlCollections = resourceCache.getAllResources();
        if (ObjectUtil.isEmpty(urlCollections)) {
            log.error(">>> 获取缓存的Resource Url为空，请检查缓存中是否被误删，requestUri={}", requestUri);
            ResponseUtil.responseExceptionError(response,
                    ServerExceptionEnum.SERVER_ERROR.getCode(),
                    ServerExceptionEnum.SERVER_ERROR.getMessage(),
                    new ServiceException(ServerExceptionEnum.SERVER_ERROR).getStackTrace()[0].toString());
            return;
        }
        String header = request.getHeader(AUTHORIZATION);


        //3.响应给前端无权限访问本接口（没有携带token）
        log.error("没有权限访问该资源，{}  requestUri = {}", header, requestUri);
        ResponseUtil.responseExceptionError(response,
                AuthExceptionEnum.REQUEST_TOKEN_EMPTY.getCode(),
                AuthExceptionEnum.REQUEST_TOKEN_EMPTY.getMessage(),
                new ServiceException(AuthExceptionEnum.REQUEST_TOKEN_EMPTY).getStackTrace()[0].toString());
    }
}
