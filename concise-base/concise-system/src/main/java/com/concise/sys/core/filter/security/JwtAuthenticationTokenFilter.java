package com.concise.sys.core.filter.security;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import com.concise.common.consts.SpringSecurityConstant;
import com.concise.common.exception.AuthException;
import com.concise.common.exception.enums.ServerExceptionEnum;
import com.concise.common.util.ResponseUtil;
import com.concise.core.login.SysLoginUser;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 这个过滤器，在所有请求之前，也在spring security filters之前
 * <p>
 * 这个过滤器的作用是：接口在进业务之前，添加登录上下文（SecurityContext）
 * <p>
 * 因为现在没有用session了，只能token来校验当前的登录人的身份，所以在进业务之前要给当前登录人设置登录状态
 *
 * <AUTHOR>
 * @date 2020/4/11 13:02
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    private static final Log log = Log.get();

    @Autowired
    private AuthService authService;

    @Autowired
    SysUserService sysUserService;

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${server.servlet.context-path}")
    private String contextPath;


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException {
        try {
            doFilter(request, response, filterChain);
        } catch (Exception e) {
            e.printStackTrace();
            ResponseUtil.responseExceptionError(response, ServerExceptionEnum.SERVER_ERROR.getCode(),
                    ServerExceptionEnum.SERVER_ERROR.getMessage(), e.getStackTrace()[0].toString());
        }
    }

    private void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {

        String requestURI = StrUtil.removePrefix(request.getRequestURI(), contextPath);
        for (String noneSecurityUrlPattern : SpringSecurityConstant.NONE_SECURITY_URL_PATTERNS) {
            if (noneSecurityUrlPattern.endsWith("/**")) {
                noneSecurityUrlPattern = StrUtil.removeSuffix(noneSecurityUrlPattern, "/**");
            }
            if (requestURI.startsWith(noneSecurityUrlPattern)) {
                filterChain.doFilter(request, response);
                return;
            }
        }

        SysLoginUser sysLoginUser = null;
        String tokenFromRequest = authService.getTokenFromRequest(request);

        try {
            if (StrUtil.isNotEmpty(tokenFromRequest)) {
                sysLoginUser = authService.getLoginUserByToken(tokenFromRequest);
            }
        } catch (AuthException ae) {
            //token过期或者token失效的情况，响应给前端
            ResponseUtil.responseExceptionError(response, ae.getCode(), ae.getErrorMessage(), ae.getStackTrace()[0].toString());
            return;
        }


        if (ObjectUtil.isNotNull(sysLoginUser)) {
            authService.setSpringSecurityContextAuthentication(sysLoginUser);
        }

        filterChain.doFilter(request, response);

    }

}
