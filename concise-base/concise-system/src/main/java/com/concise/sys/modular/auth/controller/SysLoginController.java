package com.concise.sys.modular.auth.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.text.PasswdStrength;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONObject;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.common.consts.CommonConstant;
import com.concise.common.context.constant.ConstantContextHolder;
import com.concise.common.exception.AuthException;
import com.concise.common.exception.enums.AuthExceptionEnum;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.sys.core.cache.UserCache;
import com.concise.sys.core.enums.AdminTypeEnum;
import com.concise.sys.core.jwt.JwtPayLoad;
import com.concise.sys.core.jwt.JwtTokenUtil;
import com.concise.sys.modular.app.service.SysAppService;
import com.concise.sys.modular.apppermission.sysmobileapp.service.SysMobileAppService;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.auth.zlb.SsoVo;
import com.concise.sys.modular.auth.zlb.ZlbAuthService;
import com.concise.sys.modular.emp.entity.SysEmp;
import com.concise.sys.modular.emp.service.SysEmpService;
import com.concise.sys.modular.menu.service.SysMenuService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.role.service.SysRoleService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;
import com.concise.sys.modular.user.util.SM4Util;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录控制器
 *
 * <AUTHOR>
 * @date 2020/3/11 12:20
 */
@Slf4j
@RestController
public class SysLoginController {

    @Resource
    private AuthService authService;
    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysEmpService sysEmpService;

    @Lazy
    @Resource
    private CaptchaService captchaService;

    @Resource
    private UserCache userCache;

    @Resource
    private SysAppService sysAppService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private SysMenuService sysMenuService;

    @Resource
    private ZlbAuthService zlbAuthService;

    @Resource
    private SysMobileAppService sysMobileAppService;

    @Resource
    private SysOrgService sysOrgService;


    /**
     * 获取是否开启租户的标识
     *
     * <AUTHOR>
     * @date 2020/9/4
     */
    @GetMapping("/getTenantOpen")
    public ResponseData getTenantOpen() {
        return new SuccessResponseData(ConstantContextHolder.getTenantOpenFlag());
    }

    /**
     * 账号密码登录
     *
     * <AUTHOR>
     * @date 2020/3/11 15:52
     */
    @PostMapping("/login")
    public ResponseData login(@RequestBody Dict dict) {
        String type = dict.getStr("type");
        String account = dict.getStr("account");
        String password = SM4Util.decryptDatas(dict.getStr("password"), SM4Util.SECRET_KEY);
        int check = PasswdStrength.check(password);
        boolean passwordStrong = true;
        if (check <= 6 || password.equals(CommonConstant.DEFAULT_PASSWORD)) {
            passwordStrong = false;
        }
        JSONObject jsonObject = new JSONObject();
        String token = authService.login(account, password, type);
        jsonObject.put("token", token);
        jsonObject.put("passwordStrong", passwordStrong);
        return new SuccessResponseData(jsonObject);
    }

    /**
     * 浙里办微信小程序单点登录
     */
    @ApiOperation("浙里办微信小程序单点登录")
    @PostMapping("/wechatLogin")
    public ResponseData wechatLogin(@RequestBody SsoVo ssoVo) {
        //1. 通过ticketId 换取 accessToken
        log.info(JSONObject.toJSONString(ssoVo));
        String token = zlbAuthService.getTokenByTicketId(ssoVo.getTicket());
        JSONObject userInfo = zlbAuthService.getUserInfoByToken(token);
        log.info(JSONObject.toJSONString(userInfo));
        String personInfoString = userInfo.getString("personInfo");
        JSONObject personInfo = JSONObject.parseObject(personInfoString);
        log.info(personInfoString);
        String idNum = personInfo.getString("idNo");
        log.info("浙里办单点身份证：{}", idNum);
        SysUser sysUser = sysUserService.getUserByCount(idNum);
        if (ObjectUtil.isNotEmpty(sysUser)) {
            String auth = authService.doLoginZlb(sysUser, "zlb");
            HashMap<Object, Object> map = new HashMap<>();
            map.put("tokenInfo", auth);
            map.put("userInfo", personInfo);
            return ResponseData.success(map);
        } else {
            //注册用户
            SysUser regUser = new SysUser();
            regUser.setId(personInfo.getString("id"));
            regUser.setAccount(idNum);
            regUser.setPassword(DigestUtil.md5Hex(ConstantContextHolder.getDefaultPassWord()));
            regUser.setName(personInfo.getString("userName"));
            regUser.setNickName(personInfo.getString("userName"));
            regUser.setSex(1);
            String birthday = personInfo.getString("birthday");
            if (ObjectUtil.isNotEmpty(birthday)) {
                regUser.setBirthday(DateUtil.parse(birthday, DatePattern.NORM_DATE_PATTERN));
            }
            regUser.setAdminType(AdminTypeEnum.ZLB.getCode());
            regUser.setPhone(personInfo.getString("phone"));
            regUser.setStatus(0);
            sysUserService.save(regUser);

            String auth = authService.doLoginZlb(regUser, "zlb");
            HashMap<Object, Object> map = new HashMap<>();
            map.put("tokenInfo", auth);
            map.put("userInfo", personInfo);
            return ResponseData.success(map);
        }

    }

    // 如燕随行单点登录
    @PostMapping("/sso/login")
    public ResponseData getRYLogin(@RequestBody SysUser sysUser) {
        SysUser sysUsers = sysUserService.getUserByCount(sysUser.getAccount());
        if (ObjectUtil.isEmpty(sysUsers)) {
            return ResponseData.error("用户不存在");
        }
        Map map = new HashMap();
        map.put("password", sysUsers.getPassword());
        map.put("username", sysUser.getAccount());
        return new SuccessResponseData(map);
    }


    @PostMapping("/sso/liaisonLogin")
    public ResponseData liaisonLogin(@RequestBody String tel) {
        // todo 联络员登录 这里办手机号关联
        return new SuccessResponseData();
    }

    /**
     * 退出登录
     *
     * <AUTHOR>
     * @date 2020/3/16 15:02
     */
    @GetMapping("/logout")
    public void logout() {
        authService.logout();
    }

    /**
     * 获取当前登录用户信息
     *
     * <AUTHOR>
     * @date 2020/3/23 17:57
     */
    @GetMapping("/getLoginUser")
    public ResponseData getLoginUser(HttpServletRequest request) {
        //机构id从redis中获取，解决角色菜单，数据权限 都跟机构挂钩，支持一人多机构
        String token = authService.getTokenFromRequest(request);
        JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);
        String uuid = jwtPayLoad.getUuid();
        //从redis缓存中获取登录用户
        Object cacheObject = userCache.get(jwtPayLoad.getUuid());
        //用户不存在则表示登录已过期
        if (ObjectUtil.isEmpty(cacheObject)) {
            throw new AuthException(AuthExceptionEnum.LOGIN_EXPIRED);
        }
        //转换成登录用户
        SysLoginUser sysLoginUser = (SysLoginUser) cacheObject;
        return new SuccessResponseData(LoginContextHolder.me().getSysLoginUserUpToDate(sysLoginUser.getLoginEmpInfo().getOrgId()));
    }

    /**
     * 移动端获取当前登录用户信息
     */
    @GetMapping("/getLoginUserMobile")
    public ResponseData getLoginUserMobile(HttpServletRequest request) {
        //机构id从redis中获取，解决角色菜单，数据权限 都跟机构挂钩，支持一人多机构
        String token = authService.getTokenFromRequest(request);
        JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);
        String uuid = jwtPayLoad.getUuid();
        //从redis缓存中获取登录用户
        Object cacheObject = userCache.get(jwtPayLoad.getUuid());
        //用户不存在则表示登录已过期
        if (ObjectUtil.isEmpty(cacheObject)) {
            throw new AuthException(AuthExceptionEnum.LOGIN_EXPIRED);
        }
        //转换成登录用户
        SysLoginUser sysLoginUser = (SysLoginUser) cacheObject;
        SysLoginUser loginUser = LoginContextHolder.me().getSysLoginUserUpToDate(sysLoginUser.getLoginEmpInfo().getOrgId());
        //hutool工具类脱敏

        loginUser.setName("*" + loginUser.getName().substring(1));
        if (loginUser.getNickName() != null) {
            loginUser.setNickName("*" + loginUser.getNickName().substring(1));
        }
        if (StrUtil.isNotBlank(loginUser.getAreaCode())) {
            List<String> appIds = sysMobileAppService.getAppIdsByAreaCode(sysLoginUser.getAreaCode());
            loginUser.setAppCodeList(appIds);
        } else if (StrUtil.isNotBlank(loginUser.getCityCode())) {
            List<String> appIds = sysMobileAppService.getAppIdsByAreaCode(sysLoginUser.getCityCode());
            loginUser.setAppCodeList(appIds);
        }
        return new SuccessResponseData(loginUser);
    }

    /**
     * 获取验证码开关
     *
     * <AUTHOR>
     * @date 2021/1/21 15:27
     */
    @GetMapping("/getCaptchaOpen")
    public ResponseData getCaptchaOpen() {
        return new SuccessResponseData(ConstantContextHolder.getCaptchaOpenFlag());
    }

    /**
     * 校验验证码
     *
     * <AUTHOR>
     * @date 2021/1/21 15:27
     */
    private boolean verificationCode(String code) {
        CaptchaVO vo = new CaptchaVO();
        vo.setCaptchaVerification(code);
        if (!captchaService.verification(vo).isSuccess()) {
            throw new AuthException(AuthExceptionEnum.CONSTANT_EMPTY_ERROR);
        }
        return true;
    }

    public static void main(String[] args) {
        PasswdStrength.PASSWD_LEVEL level = PasswdStrength.getLevel("5pJZSU72gwg17!cy4m");
        System.out.println(level);

    }

    @ApiOperation("查询用户的所有机构")
    @GetMapping(value = "/getUserDept")
    public ResponseData userEmps() {
        QueryWrapper<SysEmp> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysEmp::getJobNum, LoginContextHolder.me().getSysLoginUserId());
        Page<SysEmp> page = sysEmpService.page(PageFactory.defaultPage(), queryWrapper);
        List<SysEmp> records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            records.forEach(sysEmp -> {
                SysOrg sysOrg = sysOrgService.getById(sysEmp.getOrgId());
                if (ObjectUtil.isNotEmpty(sysOrg.getPid())) {
                    SysOrg porg = sysOrgService.getById(sysOrg.getPid());
                    if (ObjectUtil.isNotEmpty(porg)) {
                        sysEmp.setOrgName(porg.getName()+"-" + sysOrg.getName());
                    }
                }
            });
        }
        return ResponseData.success(records);
    }

    @ApiOperation("切换用户登录的机构")
    @GetMapping(value = "/changeUserDept")
    public ResponseData changeUserDept(HttpServletRequest request, @RequestParam String orgId) {
        String userId = LoginContextHolder.me().getSysLoginUserId();
        LambdaQueryWrapper<SysEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysEmp::getJobNum, userId);
        queryWrapper.eq(SysEmp::getOrgId, orgId);
        if (sysEmpService.count(queryWrapper) > 0) {
            SysUser sysUser = sysUserService.getById(userId);
            // String token = request.getHeader(CommonConstant.AUTHORIZATION);
            // JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token.replaceFirst(CommonConstant.TOKEN_TYPE_BEARER, ""));
            String token = authService.getTokenFromRequest(request);
            JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);
            SysLoginUser sysLoginUser = authService.genSysLoginUser(sysUser, orgId);
            sysLoginUser.setLoginEmpInfo(sysEmpService.getLoginEmpInfoByDeptId(orgId, userId));
            sysLoginUser.setDataScopes(sysUserService.getUserDataScopeIdList(userId, orgId));
            // 角色信息
            List<Dict> roles = sysRoleService.getLoginRoles(userId, orgId);
            sysLoginUser.setRoles(roles);
            //应用信息
            List<Dict> apps = sysAppService.getLoginApps(userId, orgId);
            sysLoginUser.setApps(apps);
            //菜单信息
            // 如果根本没有应用信息，则没有菜单信息
            if (ObjectUtil.isEmpty(apps)) {
                sysLoginUser.setMenus(CollectionUtil.newArrayList());
            } else {
                //AntDesign菜单信息，根据人获取，用于登录后展示菜单树，默认获取默认激活的系统的菜单
                String defaultActiveAppCode = apps.get(0).getStr(CommonConstant.CODE);
                sysLoginUser.setMenus(sysMenuService.getLoginMenusAntDesign(userId, defaultActiveAppCode, orgId));
            }
            String uuid = jwtPayLoad.getUuid();
            userCache.put(uuid, sysLoginUser, Convert.toLong(ConstantContextHolder.getSessionTokenExpireSec()));
            return ResponseData.success("切换成功！");
        }
        return ResponseData.error("用户无该机构权限！");
    }

    /**
     * 获取登录人ip
     */
    @GetMapping("/getLoginIp")
    public ResponseData getLoginIp() {
        return new SuccessResponseData(LoginContextHolder.me().getSysLoginUser().getLastLoginIp());
    }


}
