package com.concise.sys.modular.dict.result;

import com.concise.common.pojo.base.node.BaseTreeNode;
import lombok.Data;

import java.util.List;

/**
 * 系统字典树
 *
 * <AUTHOR>
 * @date 2020/3/11 12:08
 */
@Data
public class SysDictTreeNode implements BaseTreeNode {

    /**
     * id
     */
    private String id;

    /**
     * 父id
     */
    private String pid;

    /**
     * 编码-对应字典值的编码
     */
    private String code;

    /**
     * 名称-对应字典值的value
     */
    private String name;

    private Integer status;

    /**
     * 子节点集合
     */
    private List<SysDictTreeNode> children;

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getPid() {
        return this.pid;
    }

    @Override
    public void setChildren(List children) {
        this.children = children;
    }
}
