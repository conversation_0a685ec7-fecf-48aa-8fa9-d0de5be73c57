package com.concise.sys.modular.assembly.signaturemaintenance.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 签章维护表
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
@Data
@TableName("signature_maintenance")
public class SignatureMaintenance {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正单位id
     */
    private String jzjg;

    /**
     * 矫正单位
     */
    @Excel(name = " 矫正单位 ", width = 20)
    private String jzjgName;

    /**
     * 签章名称
     */
    @Excel(name = " 签章名称 ", width = 20)
    private String sealName;

    /**
     * 签章编号
     */
    @Excel(name = " 签章编号 ", width = 20)
    private String sealNo;

    /**
     * 签章类型（1-矫正机构章、2-司法局章、3-个人签名章）
     */
    @Excel(name = " 签章类型", width = 20)
    private Integer sealType;

    /**
     * 是否启用（0-启用，1-禁用）
     */
    @Excel(name = " 是否启用", width = 20)
    private Integer enabled;

    /**
     * 创建时间
     */
    @Excel(name = " 创建时间 ", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date createTime;

    /**
     * 创建人
     */
    @Excel(name = " 创建人 ", width = 20)
    private String createUser;

    /**
     * 更新时间
     */
    @Excel(name = " 更新时间 ", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date updateTime;

    /**
     * 更新人
     */
    @Excel(name = " 更新人 ", width = 20)
    private String updateUser;

}
