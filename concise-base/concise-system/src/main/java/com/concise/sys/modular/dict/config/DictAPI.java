package com.concise.sys.modular.dict.config;

import com.concise.sys.modular.dict.entity.DictModel;
import com.concise.sys.modular.dict.service.SysDictTypeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Author: luo
 * @Date: 2023-02-20 17:47
 */
@Service
public class DictAPI {
    @Resource
    private SysDictTypeService sysDictTypeService;

    public Map<String, List<DictModel>> translateManyDict(String dictCodes, String keys) {
        List<String> dictCodeList = Arrays.asList(dictCodes.split(","));
        List<String> values = Arrays.asList(keys.split(","));
        return sysDictTypeService.queryManyDictByKeys(dictCodeList, values);
    }

    public List<DictModel> translateDictFromTableByKeys(String table, String text, String code, String keys) {
        return sysDictTypeService.queryTableDictTextByKeys(table, text, code, Arrays.asList(keys.split(",")));


    }
}
