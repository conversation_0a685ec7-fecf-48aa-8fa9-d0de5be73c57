<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.sys.modular.dict.mapper.SysDictTypeMapper">

    <select id="queryManyDictByKeys" resultType="com.concise.sys.modular.dict.entity.DictModelMany">
        SELECT
        dict.code AS "dict_code",
        item.value AS "text",
        item.code AS "value"
        FROM
        sys_dict_data item
        INNER JOIN sys_dict_type dict ON dict.id = item.type_id
        WHERE dict.code IN (
        <foreach item="dictCode" collection="dictCodeList" separator=",">
            #{dictCode}
        </foreach>
        )
        AND item.code IN (
        <foreach item="key" collection="keys" separator=",">
            #{key}
        </foreach>
        )
    </select>
    <select id="codesToNames" resultType="java.lang.String">
        SELECT
            sys_dict_data.value
        FROM
            sys_dict_type
                LEFT JOIN sys_dict_data ON sys_dict_type.id = sys_dict_data.type_id
        WHERE
            sys_dict_type.`code` = #{reqType}
          AND sys_dict_data.`code` IN (
        <foreach item="key" collection="list" separator=",">
            #{key}
        </foreach>
        )
    </select>
</mapper>
