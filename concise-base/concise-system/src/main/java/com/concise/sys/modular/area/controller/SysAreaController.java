package com.concise.sys.modular.area.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.sys.modular.area.param.SysAreaParam;
import com.concise.sys.modular.area.service.SysAreaService;

import javax.annotation.Resource;

/**
 * 系统区域控制器
 *
 * <AUTHOR>
 * @date 2020/3/31 20:49
 */
@RestController
public class SysAreaController {

    @Resource
    private SysAreaService sysAreaService;

    /**
     * 系统区域列表（树）
     *
     * <AUTHOR>
     * @date 2020/3/20 21:23
     */
    @Permission
    @GetMapping("/sysArea/list")
    @BusinessLog(title = "系统区域_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(SysAreaParam sysAreaParam) {
        return new SuccessResponseData(sysAreaService.list(sysAreaParam));
    }
}
