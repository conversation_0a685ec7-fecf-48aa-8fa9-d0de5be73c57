<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.sys.modular.dict.mapper.SysDictDataMapper">

    <select id="getDictCodesByDictTypeCode" resultType="java.lang.String">
        SELECT
        dict.`code`
        FROM
        sys_dict_data dict
        INNER JOIN sys_dict_type type ON dict.type_id = type.id
        where type.code in
        <foreach collection="array" index="index" item="i" open="(" separator="," close=")">
            #{i}
        </foreach>
    </select>
    <select id="listByDictTypeCode" resultType="com.concise.sys.modular.dict.entity.SysDictData">
        SELECT
            sys_dict_data.*
        FROM
            sys_dict_type
                LEFT JOIN sys_dict_data ON sys_dict_data.type_id = sys_dict_type.id
        WHERE
            sys_dict_data.status=0 and
            sys_dict_type.`code` = #{dictTypeCode}
    </select>

</mapper>
