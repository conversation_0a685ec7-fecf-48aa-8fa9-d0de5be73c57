<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.sys.modular.apppermission.sysmobileapp.mapper.SysMobileAppMapper">

    <select id="getAppIdsByAreaCode" resultType="java.lang.String">
        SELECT
            sys_mobile_app.app_code
        FROM
            sys_mobile_app
                INNER JOIN sys_mobile_app_area ON sys_mobile_app.app_code = sys_mobile_app_area.app_code
        WHERE
            sys_mobile_app_area.area_id = #{areaCode}
    </select>
</mapper>
