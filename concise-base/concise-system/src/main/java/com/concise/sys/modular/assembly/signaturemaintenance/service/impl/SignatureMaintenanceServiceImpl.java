package com.concise.sys.modular.assembly.signaturemaintenance.service.impl;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.sys.modular.assembly.signaturemaintenance.entity.SignatureMaintenance;
import com.concise.sys.modular.assembly.signaturemaintenance.enums.SignatureMaintenanceExceptionEnum;
import com.concise.sys.modular.assembly.signaturemaintenance.mapper.SignatureMaintenanceMapper;
import com.concise.sys.modular.assembly.signaturemaintenance.param.SignatureMaintenanceParam;
import com.concise.sys.modular.assembly.signaturemaintenance.service.SignatureMaintenanceService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 签章维护表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
@Service
public class SignatureMaintenanceServiceImpl extends ServiceImpl<SignatureMaintenanceMapper, SignatureMaintenance> implements SignatureMaintenanceService {

    @Override
    public PageResult<SignatureMaintenance> page(SignatureMaintenanceParam signatureMaintenanceParam, Set<String> orgIds) {
        QueryWrapper<SignatureMaintenance> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(SignatureMaintenance::getCreateTime);
        if (CollectionUtil.isNotEmpty(orgIds)) {
            queryWrapper.lambda().in(SignatureMaintenance::getJzjg, orgIds);
        }
        if (ObjectUtil.isNotNull(signatureMaintenanceParam)) {

            // 根据矫正单位 查询
            if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getJzjgName())) {
                queryWrapper.lambda().eq(SignatureMaintenance::getJzjgName, signatureMaintenanceParam.getJzjgName());
            }
            // 根据签章名称 查询
            if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getSealName())) {
                queryWrapper.lambda().like(SignatureMaintenance::getSealName, signatureMaintenanceParam.getSealName());
            }
            // 根据签章编号 查询
            if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getSealNo())) {
                queryWrapper.lambda().like(SignatureMaintenance::getSealNo, signatureMaintenanceParam.getSealNo());
            }
            // 根据签章类型（1-矫正机构章、2-司法局章、3-个人签名章） 查询
            if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getSealType())) {
                queryWrapper.lambda().eq(SignatureMaintenance::getSealType, signatureMaintenanceParam.getSealType());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SignatureMaintenance> list(SignatureMaintenanceParam signatureMaintenanceParam, Set<String> orgSet) {
        QueryWrapper<SignatureMaintenance> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(SignatureMaintenance::getCreateTime);
        if (CollectionUtil.isNotEmpty(orgSet)) {
            queryWrapper.lambda().in(SignatureMaintenance::getJzjg, orgSet);
        }
        if (ObjectUtil.isNotNull(signatureMaintenanceParam)) {

            // 根据矫正单位 查询
            if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getJzjgName())) {
                queryWrapper.lambda().eq(SignatureMaintenance::getJzjgName, signatureMaintenanceParam.getJzjgName());
            }
            // 根据签章名称 查询
            if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getSealName())) {
                queryWrapper.lambda().like(SignatureMaintenance::getSealName, signatureMaintenanceParam.getSealName());
            }
            // 根据签章编号 查询
            if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getSealNo())) {
                queryWrapper.lambda().like(SignatureMaintenance::getSealNo, signatureMaintenanceParam.getSealNo());
            }
            // 根据签章类型（1-矫正机构章、2-司法局章、3-个人签名章） 查询
            if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getSealType())) {
                queryWrapper.lambda().eq(SignatureMaintenance::getSealType, signatureMaintenanceParam.getSealType());
            }
        }
        return this.list(queryWrapper);
    }

    @Override
    public void add(SignatureMaintenanceParam signatureMaintenanceParam) {
        SignatureMaintenance signatureMaintenance = new SignatureMaintenance();
        BeanUtil.copyProperties(signatureMaintenanceParam, signatureMaintenance);
        signatureMaintenance.setCreateTime(DateUtil.date());
        signatureMaintenance.setCreateUser(signatureMaintenanceParam.getCreateUser());
        this.save(signatureMaintenance);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SignatureMaintenanceParam signatureMaintenanceParam) {
        this.removeById(signatureMaintenanceParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SignatureMaintenanceParam signatureMaintenanceParam) {
        SignatureMaintenance signatureMaintenance = this.querySignatureMaintenance(signatureMaintenanceParam);
        BeanUtil.copyProperties(signatureMaintenanceParam, signatureMaintenance);
        this.updateById(signatureMaintenance);
    }

    @Override
    public SignatureMaintenance detail(SignatureMaintenanceParam signatureMaintenanceParam) {
        return this.querySignatureMaintenance(signatureMaintenanceParam);
    }

    /**
     * 获取签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    private SignatureMaintenance querySignatureMaintenance(SignatureMaintenanceParam signatureMaintenanceParam) {
        SignatureMaintenance signatureMaintenance = this.getById(signatureMaintenanceParam.getId());
        if (ObjectUtil.isNull(signatureMaintenance)) {
            throw new ServiceException(SignatureMaintenanceExceptionEnum.NOT_EXIST);
        }
        return signatureMaintenance;
    }

    @Override
    public void export(SignatureMaintenanceParam signatureMaintenanceParam, Set<String> orgSet) {
        List<SignatureMaintenance> list = this.list(signatureMaintenanceParam, orgSet);
        PoiUtil.exportExcelWithStream("签章管理.xls", SignatureMaintenance.class, list);
    }

    @Override
    public void changeStatus(SignatureMaintenanceParam signatureMaintenanceParam) {
        SignatureMaintenance signatureMaintenance = this.getById(signatureMaintenanceParam.getId());
        signatureMaintenance.setEnabled(signatureMaintenanceParam.getEnabled());
        this.updateById(signatureMaintenance);
    }
}
