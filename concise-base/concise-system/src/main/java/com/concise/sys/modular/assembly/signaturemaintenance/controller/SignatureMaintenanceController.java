package com.concise.sys.modular.assembly.signaturemaintenance.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.sys.modular.assembly.signaturemaintenance.entity.SignatureMaintenance;
import com.concise.sys.modular.assembly.signaturemaintenance.param.SignatureMaintenanceParam;
import com.concise.sys.modular.assembly.signaturemaintenance.service.SignatureMaintenanceService;
import com.concise.sys.modular.org.service.SysOrgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

/**
 * 签章维护表控制器
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
@Api(tags = "签章维护表")
@RestController
public class SignatureMaintenanceController {

    @Resource
    private SignatureMaintenanceService signatureMaintenanceService;

    @Resource
    private SysOrgService sysOrgService;

    /**
     * 查询签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    @Permission
    @GetMapping("/signatureMaintenance/page")
    @ApiOperation("签章维护表_分页查询")
    public ResponseData page(SignatureMaintenanceParam signatureMaintenanceParam) {
        Set<String> orgIdSet = new HashSet<>();
        if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getJzjg())) {
            orgIdSet = sysOrgService.getOrgIdSet(signatureMaintenanceParam.getJzjg());
        } else if (!LoginContextHolder.me().isSuperAdmin()) {
            orgIdSet = sysOrgService.getOrgIdSet(LoginContextHolder.me().getSysLoginUserOrgId());
        }
        return new SuccessResponseData(signatureMaintenanceService.page(signatureMaintenanceParam, orgIdSet));
    }

    /**
     * 添加签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    @Permission
    @PostMapping("/signatureMaintenance/add")
    @ApiOperation("签章维护表_增加")
    @BusinessLog(title = "签章维护表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(SignatureMaintenanceParam.add.class) SignatureMaintenanceParam signatureMaintenanceParam) {
        int count = signatureMaintenanceService.count(new QueryWrapper<SignatureMaintenance>().lambda().eq(SignatureMaintenance::getJzjg, signatureMaintenanceParam.getJzjg()).eq(SignatureMaintenance::getSealType, signatureMaintenanceParam.getSealType()));
        if (count > 0) {
            return ResponseData.error("当前单位已经存在同类型签章，请先删除或者直接在原签章处修改！");
        }
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        signatureMaintenanceParam.setCreateUser(sysLoginUser.getName());
        signatureMaintenanceService.add(signatureMaintenanceParam);
        return new SuccessResponseData();
    }

    /**
     * 删除签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    @Permission
    @PostMapping("/signatureMaintenance/delete")
    @ApiOperation("签章维护表_删除")
    @BusinessLog(title = "签章维护表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(SignatureMaintenanceParam.delete.class) SignatureMaintenanceParam signatureMaintenanceParam) {
        signatureMaintenanceService.delete(signatureMaintenanceParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    @Permission
    @PostMapping("/signatureMaintenance/edit")
    @ApiOperation("签章维护表_编辑")
    @BusinessLog(title = "签章维护表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(SignatureMaintenanceParam.edit.class) SignatureMaintenanceParam signatureMaintenanceParam) {
        signatureMaintenanceService.edit(signatureMaintenanceParam);
        return new SuccessResponseData();
    }

    /**
     * 查看签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    @Permission
    @GetMapping("/signatureMaintenance/detail")
    @ApiOperation("签章维护表_查看")
    public ResponseData detail(@Validated(SignatureMaintenanceParam.detail.class) SignatureMaintenanceParam signatureMaintenanceParam) {
        return new SuccessResponseData(signatureMaintenanceService.detail(signatureMaintenanceParam));
    }

    /**
     * 签章维护表列表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    @Permission
    @GetMapping("/signatureMaintenance/list")
    @ApiOperation("签章维护表_列表")
    public ResponseData list(SignatureMaintenanceParam signatureMaintenanceParam) {
        Set<String> orgIdSet = new HashSet<>();
        if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getJzjg())) {
            orgIdSet = sysOrgService.getOrgIdSet(signatureMaintenanceParam.getJzjg());
        } else if (!LoginContextHolder.me().isSuperAdmin()) {
            orgIdSet = sysOrgService.getOrgIdSet(LoginContextHolder.me().getSysLoginUserOrgId());
        }
        return new SuccessResponseData(signatureMaintenanceService.list(signatureMaintenanceParam, orgIdSet));
    }

    @GetMapping("/signatureMaintenance/export")
    @BusinessLog(title = "签章维护表_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(SignatureMaintenanceParam signatureMaintenanceParam) {
        Set<String> orgIdSet = new HashSet<>();
        if (ObjectUtil.isNotEmpty(signatureMaintenanceParam.getJzjg())) {
            orgIdSet = sysOrgService.getOrgIdSet(signatureMaintenanceParam.getJzjg());
        } else if (!LoginContextHolder.me().isSuperAdmin()) {
            orgIdSet = sysOrgService.getOrgIdSet(LoginContextHolder.me().getSysLoginUserOrgId());
        }
        signatureMaintenanceService.export(signatureMaintenanceParam, orgIdSet);
    }

    @PostMapping("/signatureMaintenance/changeStatus")
    @ApiOperation("签章维护表_启用禁用")
    @BusinessLog(title = "签章维护表_启用禁用", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData changeStatus(@RequestBody SignatureMaintenanceParam signatureMaintenanceParam) {
        signatureMaintenanceService.changeStatus(signatureMaintenanceParam);
        return new SuccessResponseData();
    }

}
