package com.concise.sys.core.jwt;

import cn.hutool.core.util.IdUtil;
import lombok.Data;

/**
 * JwtPayLoad部分
 *
 * <AUTHOR>
 * @date 2020/3/12 17:41
 */
@Data
public class JwtPayLoad {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 账号
     */
    private String account;

    /**
     * 唯一表示id, 用于缓存登录用户的唯一凭证
     */
    private String uuid;

    public JwtPayLoad() {
    }

    public JwtPayLoad(String userId, String account) {
        this.userId = userId;
        this.account = account;
        this.uuid = IdUtil.fastUUID();
    }
}
