package com.concise.sys.modular.apppermission.sysmobileapparea.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.concise.common.pojo.base.entity.BaseEntity;

/**
 * 移动端权限区域表
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:09
 */
@Data
@TableName("sys_mobile_app_area")
public class SysMobileAppArea{

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 区域id
     */
    private String areaId;

}
