package com.concise.sys.modular.apppermission.sysmobileapp.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.sys.modular.apppermission.sysmobileapp.param.SysMobileAppParam;
import com.concise.sys.modular.apppermission.sysmobileapp.service.SysMobileAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 移动端权限控制器
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:07
 */
@Api(tags = "移动端权限")
@RestController
public class SysMobileAppController {

    @Resource
    private SysMobileAppService sysMobileAppService;

    /**
     * 查询移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    @Permission
    @GetMapping("/sysMobileApp/page")
    @ApiOperation("移动端权限_分页查询")
    @BusinessLog(title = "移动端权限_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(SysMobileAppParam sysMobileAppParam) {
        return new SuccessResponseData(sysMobileAppService.page(sysMobileAppParam));
    }

    /**
     * 添加移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    @Permission
    @PostMapping("/sysMobileApp/add")
    @ApiOperation("移动端权限_增加")
    @BusinessLog(title = "移动端权限_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(SysMobileAppParam.add.class) SysMobileAppParam sysMobileAppParam) {
        sysMobileAppService.add(sysMobileAppParam);
        return new SuccessResponseData();
    }

    /**
     * 删除移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    @Permission
    @PostMapping("/sysMobileApp/delete")
    @ApiOperation("移动端权限_删除")
    @BusinessLog(title = "移动端权限_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(SysMobileAppParam.delete.class) SysMobileAppParam sysMobileAppParam) {
        sysMobileAppService.delete(sysMobileAppParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    @Permission
    @PostMapping("/sysMobileApp/edit")
    @ApiOperation("移动端权限_编辑")
    @BusinessLog(title = "移动端权限_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(SysMobileAppParam.edit.class) SysMobileAppParam sysMobileAppParam) {
        sysMobileAppService.edit(sysMobileAppParam);
        return new SuccessResponseData();
    }

    /**
     * 查看移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    @Permission
    @GetMapping("/sysMobileApp/detail")
    @ApiOperation("移动端权限_查看")
    @BusinessLog(title = "移动端权限_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(SysMobileAppParam.detail.class) SysMobileAppParam sysMobileAppParam) {
        return new SuccessResponseData(sysMobileAppService.detail(sysMobileAppParam));
    }

    /**
     * 移动端权限列表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    @Permission
    @GetMapping("/sysMobileApp/list")
    @ApiOperation("移动端权限_列表")
    @BusinessLog(title = "移动端权限_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(SysMobileAppParam sysMobileAppParam) {
        return new SuccessResponseData(sysMobileAppService.list(sysMobileAppParam));
    }

    /**
     * 授权
     */
    @Permission
    @PostMapping("/sysMobileApp/auth")
    @ApiOperation("移动端权限_授权")
    public ResponseData auth(@RequestBody SysMobileAppParam sysMobileAppParam) {
        sysMobileAppService.auth(sysMobileAppParam);
        return new SuccessResponseData();
    }

    /**
     * 获取已授权的区域
     */
    @Permission
    @GetMapping("/sysMobileApp/getAreaCodes")
    @ApiOperation("移动端权限_获取授权的区域")
    public ResponseData getAreaCodes(SysMobileAppParam sysMobileAppParam) {
        return new SuccessResponseData(sysMobileAppService.getAreaCodes(sysMobileAppParam));
    }



}
