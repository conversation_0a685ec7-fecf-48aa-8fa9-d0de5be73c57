package com.concise.sys.modular.apppermission.sysmobileapp.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.node.AntdBaseTreeNode;
import com.concise.common.pojo.page.PageResult;
import com.concise.sys.modular.apppermission.sysmobileapp.entity.SysMobileApp;
import com.concise.sys.modular.apppermission.sysmobileapp.enums.SysMobileAppExceptionEnum;
import com.concise.sys.modular.apppermission.sysmobileapp.mapper.SysMobileAppMapper;
import com.concise.sys.modular.apppermission.sysmobileapp.param.SysMobileAppParam;
import com.concise.sys.modular.apppermission.sysmobileapp.service.SysMobileAppService;
import com.concise.sys.modular.apppermission.sysmobileapparea.entity.SysMobileAppArea;
import com.concise.sys.modular.apppermission.sysmobileapparea.service.SysMobileAppAreaService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 移动端权限service接口实现类
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:07
 */
@Service
public class SysMobileAppServiceImpl extends ServiceImpl<SysMobileAppMapper, SysMobileApp> implements SysMobileAppService {
    @Resource
    private SysMobileAppAreaService sysMobileAppAreaService;


    @Override
    public PageResult<SysMobileApp> page(SysMobileAppParam sysMobileAppParam) {
        QueryWrapper<SysMobileApp> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(sysMobileAppParam)) {

            // 根据应用编码 查询
            if (ObjectUtil.isNotEmpty(sysMobileAppParam.getAppCode())) {
                queryWrapper.lambda().like(SysMobileApp::getAppCode, sysMobileAppParam.getAppCode());
            }
            // 根据应用名称 查询
            if (ObjectUtil.isNotEmpty(sysMobileAppParam.getAppName())) {
                queryWrapper.lambda().like(SysMobileApp::getAppName, sysMobileAppParam.getAppName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SysMobileApp> list(SysMobileAppParam sysMobileAppParam) {
        return this.list();
    }

    @Override
    public void add(SysMobileAppParam sysMobileAppParam) {
        SysMobileApp sysMobileApp = new SysMobileApp();
        BeanUtil.copyProperties(sysMobileAppParam, sysMobileApp);
        this.save(sysMobileApp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SysMobileAppParam sysMobileAppParam) {
        this.removeById(sysMobileAppParam.getAppCode());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SysMobileAppParam sysMobileAppParam) {
        SysMobileApp sysMobileApp = this.querySysMobileApp(sysMobileAppParam);
        BeanUtil.copyProperties(sysMobileAppParam, sysMobileApp);
        this.updateById(sysMobileApp);
    }

    @Override
    public SysMobileApp detail(SysMobileAppParam sysMobileAppParam) {
        return this.querySysMobileApp(sysMobileAppParam);
    }

    /**
     * 获取移动端权限
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:07
     */
    private SysMobileApp querySysMobileApp(SysMobileAppParam sysMobileAppParam) {
        SysMobileApp sysMobileApp = this.getById(sysMobileAppParam.getAppCode());
        if (ObjectUtil.isNull(sysMobileApp)) {
            throw new ServiceException(SysMobileAppExceptionEnum.NOT_EXIST);
        }
        return sysMobileApp;
    }

    @Override
    public List<String> getAppIdsByAreaCode(String areaCode) {
        List<String> appIds = this.baseMapper.getAppIdsByAreaCode(areaCode);
        if (ObjectUtil.isNotEmpty(appIds)) {
            return appIds;
        }
        return Collections.emptyList();
    }

    @Override
    public void auth(SysMobileAppParam sysMobileAppParam) {
        // 1. 获取上次授权的权限id
        List<String> lastPermissionIds = sysMobileAppParam.getLastPermissionIds();
        // 2. 获取当前授权的权限id
        List<String> permissionIds = sysMobileAppParam.getPermissionIds();
        // 3. 更新授权的权限id
        sysMobileAppAreaService.remove(new QueryWrapper<SysMobileAppArea>().lambda().eq(SysMobileAppArea::getAppCode, sysMobileAppParam.getAppCode()));

        if (ObjectUtil.isNotEmpty(permissionIds)) {
            List<SysMobileAppArea> sysMobileAppAreas = new ArrayList<>();
            for (String permissionId : permissionIds) {
                SysMobileAppArea sysMobileAppArea = new SysMobileAppArea();
                sysMobileAppArea.setAreaId(permissionId);
                sysMobileAppArea.setAppCode(sysMobileAppParam.getAppCode());
                sysMobileAppAreas.add(sysMobileAppArea);
            }
            sysMobileAppAreaService.saveBatch(sysMobileAppAreas);
        }
    }

    @Override
    public List<String> getAreaCodes(SysMobileAppParam sysMobileAppParam) {
        return sysMobileAppAreaService.list(new QueryWrapper<SysMobileAppArea>().lambda().eq(SysMobileAppArea::getAppCode, sysMobileAppParam.getAppCode())).stream().map(SysMobileAppArea::getAreaId).collect(Collectors.toList());
    }


}
