package com.concise.sys.modular.activiti6.service.impl;

import cn.hutool.http.HttpRequest;
import com.concise.common.util.FileUtils;
import com.concise.sys.modular.activiti6.respones.ProcessListResp;
import com.concise.sys.modular.activiti6.service.ApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-02-20 15:10:31
 */
@Service
@Slf4j
public class ApprovalServiceImpl implements ApprovalService {

    @Autowired
    private RepositoryService repositoryService;



    @Override
    public List<ProcessListResp> processList() {
        ProcessDefinitionQuery query = repositoryService.createProcessDefinitionQuery();
        query.latestVersion();
        List<ProcessDefinition> processDefinitions = query.listPage(0, 100);
        List<ProcessListResp> processList = new ArrayList<>();
        processDefinitions.stream().forEach(item -> {
            processList.add(new ProcessListResp(item.getId(), item.getKey(), item.getDeploymentId(), item.getVersion(),item.getName()));
        });
        return processList;
    }


}


