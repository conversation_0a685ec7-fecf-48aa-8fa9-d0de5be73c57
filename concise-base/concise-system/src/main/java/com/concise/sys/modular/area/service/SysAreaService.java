package com.concise.sys.modular.area.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.sys.modular.area.entity.SysArea;
import com.concise.sys.modular.area.param.SysAreaParam;

import java.util.List;

/**
 * 系统字典值service接口
 *
 * <AUTHOR>
 * @date 2020/3/13 16:10
 */
public interface SysAreaService extends IService<SysArea> {

    /**
     * 系统区域列表（树表）
     *
     * @param sysAreaParam 查询参数
     * @return 区域树表列表
     * <AUTHOR>
     * @date 2020/3/26 10:19
     */
    List<SysArea> list(SysAreaParam sysAreaParam);
}
