package com.concise.sys.modular.assembly.signaturemaintenance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.sys.modular.assembly.signaturemaintenance.entity.SignatureMaintenance;
import com.concise.sys.modular.assembly.signaturemaintenance.param.SignatureMaintenanceParam;

import java.util.List;
import java.util.Set;

/**
 * 签章维护表service接口
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
public interface SignatureMaintenanceService extends IService<SignatureMaintenance> {

    /**
     * 查询签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    PageResult<SignatureMaintenance> page(SignatureMaintenanceParam signatureMaintenanceParam, Set<String> orgIds);

    /**
     * 签章维护表列表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    List<SignatureMaintenance> list(SignatureMaintenanceParam signatureMaintenanceParam,Set<String> orgSet);

    /**
     * 添加签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    void add(SignatureMaintenanceParam signatureMaintenanceParam);

    /**
     * 删除签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    void delete(SignatureMaintenanceParam signatureMaintenanceParam);

    /**
     * 编辑签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    void edit(SignatureMaintenanceParam signatureMaintenanceParam);

    /**
     * 查看签章维护表
     *
     * <AUTHOR>
     * @date 2024-01-19 11:21:29
     */
    SignatureMaintenance detail(SignatureMaintenanceParam signatureMaintenanceParam);

    /**
     * 批量导出
     *
     * @param signatureMaintenanceParam
     */
    void export(SignatureMaintenanceParam signatureMaintenanceParam,Set<String> orgSet);

    /**
     * 启用禁用
     *
     * @param signatureMaintenanceParam
     */
    void changeStatus(SignatureMaintenanceParam signatureMaintenanceParam);
}
