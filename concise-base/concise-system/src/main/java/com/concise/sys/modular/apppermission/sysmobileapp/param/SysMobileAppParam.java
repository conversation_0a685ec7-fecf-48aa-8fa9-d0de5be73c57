package com.concise.sys.modular.apppermission.sysmobileapp.param;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.concise.common.pojo.base.param.BaseParam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 移动端权限参数类
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:07
*/
@Data
public class SysMobileAppParam extends BaseParam {

    /**
     * 应用编码
     */
    @ApiModelProperty(value = "应用编码")
    @NotNull(message = "应用编码不能为空，请检查appCode参数", groups = {edit.class, delete.class, detail.class})
    private String appCode;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    @NotBlank(message = "应用名称不能为空，请检查appName参数", groups = {add.class, edit.class})
    private String appName;

    /**
     * 权限id
     */
    @ApiModelProperty(value = "权限id")
    private List<String> permissionIds;

    /**
     * 上次授权的权限id
     */
    @ApiModelProperty(value = "上次授权的权限id")
    private List<String> lastPermissionIds;

}
