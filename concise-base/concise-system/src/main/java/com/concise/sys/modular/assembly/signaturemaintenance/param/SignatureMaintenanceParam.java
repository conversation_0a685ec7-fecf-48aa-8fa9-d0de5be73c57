package com.concise.sys.modular.assembly.signaturemaintenance.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 签章维护表参数类
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
*/
@Data
public class SignatureMaintenanceParam extends BaseParam {

    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 矫正单位id
     */
    @NotBlank(message = "矫正单位id不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 矫正单位
     */
    @NotBlank(message = "矫正单位不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    private String jzjgName;

    /**
     * 签章名称
     */
    @NotBlank(message = "签章名称不能为空，请检查sealName参数", groups = {add.class, edit.class})
    private String sealName;

    /**
     * 签章编号
     */
    @NotBlank(message = "签章编号不能为空，请检查sealNo参数", groups = {add.class, edit.class})
    private String sealNo;

    /**
     * 签章类型（1-矫正机构章、2-司法局章、3-个人签名章）
     */
    @NotNull(message = "签章类型（1-矫正机构章、2-司法局章、3-个人签名章）不能为空，请检查sealType参数", groups = {add.class, edit.class})
    private Integer sealType;

    /**
     * 是否启用（0-启用，1-禁用）
     */
    @NotNull(message = "是否启用（0-启用，1-禁用）不能为空，请检查enabled参数", groups = {add.class, edit.class})
    private Integer enabled;

    private String createUser;

    private String updateUser;

}
