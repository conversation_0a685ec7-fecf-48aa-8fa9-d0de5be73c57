package com.concise.sys.modular.apppermission.sysmobileapparea.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.sys.modular.apppermission.sysmobileapparea.entity.SysMobileAppArea;
import com.concise.sys.modular.apppermission.sysmobileapparea.enums.SysMobileAppAreaExceptionEnum;
import com.concise.sys.modular.apppermission.sysmobileapparea.mapper.SysMobileAppAreaMapper;
import com.concise.sys.modular.apppermission.sysmobileapparea.param.SysMobileAppAreaParam;
import com.concise.sys.modular.apppermission.sysmobileapparea.service.SysMobileAppAreaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 移动端权限区域表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:09
 */
@Service
public class SysMobileAppAreaServiceImpl extends ServiceImpl<SysMobileAppAreaMapper, SysMobileAppArea> implements SysMobileAppAreaService {

    @Override
    public PageResult<SysMobileAppArea> page(SysMobileAppAreaParam sysMobileAppAreaParam) {
        QueryWrapper<SysMobileAppArea> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(sysMobileAppAreaParam)) {

            // 根据应用编码 查询
            if (ObjectUtil.isNotEmpty(sysMobileAppAreaParam.getAppCode())) {
                queryWrapper.lambda().eq(SysMobileAppArea::getAppCode, sysMobileAppAreaParam.getAppCode());
            }
            // 根据区域id 查询
            if (ObjectUtil.isNotEmpty(sysMobileAppAreaParam.getAreaId())) {
                queryWrapper.lambda().eq(SysMobileAppArea::getAreaId, sysMobileAppAreaParam.getAreaId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SysMobileAppArea> list(SysMobileAppAreaParam sysMobileAppAreaParam) {
        return this.list();
    }

    @Override
    public void add(SysMobileAppAreaParam sysMobileAppAreaParam) {
        SysMobileAppArea sysMobileAppArea = new SysMobileAppArea();
        BeanUtil.copyProperties(sysMobileAppAreaParam, sysMobileAppArea);
        this.save(sysMobileAppArea);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SysMobileAppAreaParam sysMobileAppAreaParam) {
        this.removeById(sysMobileAppAreaParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SysMobileAppAreaParam sysMobileAppAreaParam) {
        SysMobileAppArea sysMobileAppArea = this.querySysMobileAppArea(sysMobileAppAreaParam);
        BeanUtil.copyProperties(sysMobileAppAreaParam, sysMobileAppArea);
        this.updateById(sysMobileAppArea);
    }

    @Override
    public SysMobileAppArea detail(SysMobileAppAreaParam sysMobileAppAreaParam) {
        return this.querySysMobileAppArea(sysMobileAppAreaParam);
    }

    /**
     * 获取移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    private SysMobileAppArea querySysMobileAppArea(SysMobileAppAreaParam sysMobileAppAreaParam) {
        SysMobileAppArea sysMobileAppArea = this.getById(sysMobileAppAreaParam.getId());
        if (ObjectUtil.isNull(sysMobileAppArea)) {
            throw new ServiceException(SysMobileAppAreaExceptionEnum.NOT_EXIST);
        }
        return sysMobileAppArea;
    }
}
