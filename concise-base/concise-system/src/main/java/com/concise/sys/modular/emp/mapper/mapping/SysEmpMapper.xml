<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.sys.modular.emp.mapper.SysEmpMapper">

    <select id="getExcessOrgEmp" resultType="com.concise.sys.modular.emp.entity.SysEmp">
        SELECT sys_emp.*
        FROM sys_emp
                 LEFT JOIN sys_user ON sys_user.id = sys_emp.job_num
        WHERE sys_user.id IS NULL
    </select>
</mapper>
