package com.concise.sys.modular.apppermission.sysmobileapparea.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.sys.modular.apppermission.sysmobileapparea.param.SysMobileAppAreaParam;
import com.concise.sys.modular.apppermission.sysmobileapparea.service.SysMobileAppAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 移动端权限区域表控制器
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:09
 */
@Api(tags = "移动端权限区域表")
@RestController
public class SysMobileAppAreaController {

    @Resource
    private SysMobileAppAreaService sysMobileAppAreaService;

    /**
     * 查询移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    @Permission
    @GetMapping("/sysMobileAppArea/page")
    @ApiOperation("移动端权限区域表_分页查询")
    @BusinessLog(title = "移动端权限区域表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(SysMobileAppAreaParam sysMobileAppAreaParam) {
        return new SuccessResponseData(sysMobileAppAreaService.page(sysMobileAppAreaParam));
    }

    /**
     * 添加移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    @Permission
    @PostMapping("/sysMobileAppArea/add")
    @ApiOperation("移动端权限区域表_增加")
    @BusinessLog(title = "移动端权限区域表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(SysMobileAppAreaParam.add.class) SysMobileAppAreaParam sysMobileAppAreaParam) {
        sysMobileAppAreaService.add(sysMobileAppAreaParam);
        return new SuccessResponseData();
    }

    /**
     * 删除移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    @Permission
    @PostMapping("/sysMobileAppArea/delete")
    @ApiOperation("移动端权限区域表_删除")
    @BusinessLog(title = "移动端权限区域表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(SysMobileAppAreaParam.delete.class) SysMobileAppAreaParam sysMobileAppAreaParam) {
        sysMobileAppAreaService.delete(sysMobileAppAreaParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    @Permission
    @PostMapping("/sysMobileAppArea/edit")
    @ApiOperation("移动端权限区域表_编辑")
    @BusinessLog(title = "移动端权限区域表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(SysMobileAppAreaParam.edit.class) SysMobileAppAreaParam sysMobileAppAreaParam) {
        sysMobileAppAreaService.edit(sysMobileAppAreaParam);
        return new SuccessResponseData();
    }

    /**
     * 查看移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    @Permission
    @GetMapping("/sysMobileAppArea/detail")
    @ApiOperation("移动端权限区域表_查看")
    @BusinessLog(title = "移动端权限区域表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(SysMobileAppAreaParam.detail.class) SysMobileAppAreaParam sysMobileAppAreaParam) {
        return new SuccessResponseData(sysMobileAppAreaService.detail(sysMobileAppAreaParam));
    }

    /**
     * 移动端权限区域表列表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    @Permission
    @GetMapping("/sysMobileAppArea/list")
    @ApiOperation("移动端权限区域表_列表")
    @BusinessLog(title = "移动端权限区域表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(SysMobileAppAreaParam sysMobileAppAreaParam) {
        return new SuccessResponseData(sysMobileAppAreaService.list(sysMobileAppAreaParam));
    }

}
