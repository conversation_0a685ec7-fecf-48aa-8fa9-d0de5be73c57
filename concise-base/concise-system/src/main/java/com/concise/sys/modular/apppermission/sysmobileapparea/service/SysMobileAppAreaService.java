package com.concise.sys.modular.apppermission.sysmobileapparea.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.sys.modular.apppermission.sysmobileapparea.entity.SysMobileAppArea;
import com.concise.sys.modular.apppermission.sysmobileapparea.param.SysMobileAppAreaParam;
import java.util.List;

/**
 * 移动端权限区域表service接口
 *
 * <AUTHOR>
 * @date 2024-12-25 13:38:09
 */
public interface SysMobileAppAreaService extends IService<SysMobileAppArea> {

    /**
     * 查询移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    PageResult<SysMobileAppArea> page(SysMobileAppAreaParam sysMobileAppAreaParam);

    /**
     * 移动端权限区域表列表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    List<SysMobileAppArea> list(SysMobileAppAreaParam sysMobileAppAreaParam);

    /**
     * 添加移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    void add(SysMobileAppAreaParam sysMobileAppAreaParam);

    /**
     * 删除移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    void delete(SysMobileAppAreaParam sysMobileAppAreaParam);

    /**
     * 编辑移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
    void edit(SysMobileAppAreaParam sysMobileAppAreaParam);

    /**
     * 查看移动端权限区域表
     *
     * <AUTHOR>
     * @date 2024-12-25 13:38:09
     */
     SysMobileAppArea detail(SysMobileAppAreaParam sysMobileAppAreaParam);
}
