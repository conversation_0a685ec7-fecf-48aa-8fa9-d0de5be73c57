package com.concise.generate.core.tool;

/**
 * java与sql工具类
 *
 * <AUTHOR>
 * @date 2020-12-17 23:42
 */
public class JavaSqlTool {

    /**
     * 数据类型转化JAVA
     *
     * <AUTHOR>
     * @date 2020-12-17 23:42
     */
    public static String sqlToJava (String sqlType) {
        if( sqlType == null || sqlType.trim().length() == 0 ) return sqlType;
        sqlType = sqlType.toLowerCase();
        if(sqlType.startsWith("int")) {
            //如果以int开头，则直接返回int，兼容pgsql中int2 int8等
            return "Integer";
        }
        switch(sqlType){
            case "nvarchar":return "String";
            case "nvarchar2":return "String";
            case "char":return "String";
            case "varchar":return "String";
            case "enum":return "String";
            case "se/**\n" +
                    " * <AUTHOR> +
                    " * @version 1.0.0\n" +
                    " * @createTime 2021-07-19 17:53:55\n" +
                    " * @Description 脑图转SQL工具类\n" +
                    " */\n" +
                    "DROP TABLE IF EXISTS `legal_aid_cases`;\n" +
                    "CREATE TABLE `legal_aid_cases` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '法律援助案件主键',\n" +
                    "  `case_num` varchar(255) DEFAULT NULL COMMENT '案号',\n" +
                    "  `dic_case_type` varchar(255) DEFAULT NULL COMMENT '案件类型 民事案件、刑事案件、行政案件',\n" +
                    "  `case_detail` varchar(255) DEFAULT NULL COMMENT '案情概况',\n" +
                    "  `dic_involve_count` varchar(255) DEFAULT NULL COMMENT '案件涉及多人 N：个人案件，M:群体性案件',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='法律援助案件';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `legal_help_people_temp`;\n" +
                    "CREATE TABLE `legal_help_people_temp` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '承办援助人与案件关联信息表主键',\n" +
                    "  `t_case_id` bigint(20) DEFAULT NULL COMMENT '案件id',\n" +
                    "  `t_org_id` bigint(20) DEFAULT NULL COMMENT '承办人机构id',\n" +
                    "  `t_hp_user_id` bigint(20) DEFAULT NULL COMMENT '承办援助人id',\n" +
                    "  `hp_work_unit` varchar(255) DEFAULT NULL COMMENT '承办援助人工作单位',\n" +
                    "  `hp_name` varchar(255) DEFAULT NULL COMMENT '承办人（援助人员姓名）',\n" +
                    "  `dic_hp_identity` varchar(255) DEFAULT NULL COMMENT '法律援助人员类型',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='承办援助人与案件关联信息表';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `legal_accept_people`;\n" +
                    "CREATE TABLE `legal_accept_people` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '受援人与案件关联表主键',\n" +
                    "  `t_case_id` bigint(20) DEFAULT NULL COMMENT '案件id',\n" +
                    "  `name` varchar(255) DEFAULT NULL COMMENT '姓名',\n" +
                    "  `dic_card_type` varchar(255) DEFAULT NULL COMMENT '证件类型',\n" +
                    "  `card_code` varchar(255) DEFAULT NULL COMMENT '证件号',\n" +
                    "  `dic_gender` varchar(255) DEFAULT NULL COMMENT '性别',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='受援人与案件关联表';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `legal_evaluate`;\n" +
                    "CREATE TABLE `legal_evaluate` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '法律援助评价信息表主键',\n" +
                    "  `t_case_id` bigint(20) DEFAULT NULL COMMENT '案件ID',\n" +
                    "  `dic_summary` varchar(255) DEFAULT NULL COMMENT '受援人总体评价',\n" +
                    "  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='法律援助评价信息表';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `t_orm_judge_opinion`;\n" +
                    "CREATE TABLE `t_orm_judge_opinion` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '法官评价信息（t_orm_judge_opinion）主键',\n" +
                    "  `t_case_id` bigint(20) DEFAULT NULL COMMENT '案件id 未评价',\n" +
                    "  `dic_etiquette` varchar(255) DEFAULT NULL COMMENT '诉讼礼仪 诉讼礼仪：1、满意 2、基本满意 3、不满意',\n" +
                    "  `dic_accomplishment` varchar(255) DEFAULT NULL COMMENT '专业素养 专业素养 ：1、满意 2、基本满意 3、不满意',\n" +
                    "  `dic_dedication` varchar(255) DEFAULT NULL COMMENT '敬业精神 敬业精神： 1、满意 2、基本满意 3、不满意',\n" +
                    "  `suggestion` varchar(255) DEFAULT NULL COMMENT '法官意见',\n" +
                    "  `dic_status` varchar(255) DEFAULT NULL COMMENT '状态 状态：1、未评价 2、已评价',\n" +
                    "  `dic_method` varchar(255) DEFAULT NULL COMMENT '评价方式 评价方式 1、自评  2、系统评',\n" +
                    "  `dic_summary` varchar(255) DEFAULT NULL COMMENT '总体评价 总体评价 1、满意 2、基本满意 3、不满意',\n" +
                    "  `is_evaluate` varchar(255) DEFAULT NULL COMMENT '是否能评价 是否能评价(0.能，1.不能）',\n" +
                    "  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='法官评价信息（t_orm_judge_opinion）';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `sifa_case_sms_grade`;\n" +
                    "CREATE TABLE `sifa_case_sms_grade` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '短信评价（sifa_case_sms_grade）主键',\n" +
                    "  `caseid` bigint(20) DEFAULT NULL COMMENT '案件',\n" +
                    "  `litigantid` bigint(20) DEFAULT NULL COMMENT '短信评价人id',\n" +
                    "  `litigantname` varchar(255) DEFAULT NULL COMMENT '短信评价人',\n" +
                    "  `grade` varchar(255) DEFAULT NULL COMMENT '评分等级 很不满意、不满意、一般、满意、非常满意',\n" +
                    "  `ajbm` varchar(255) DEFAULT NULL COMMENT '案件编码',\n" +
                    "  `ajly` varchar(255) DEFAULT NULL COMMENT '案件来源',\n" +
                    "  `suggest` varchar(255) DEFAULT NULL COMMENT '建议',\n" +
                    "  `messageid` bigint(20) DEFAULT NULL COMMENT '短信ID',\n" +
                    "  `status` varchar(255) DEFAULT NULL COMMENT '评分状态 0-未评分 1-已评分 2-超时系统默认评分',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='短信评价（sifa_case_sms_grade）';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `legal_mediators`;\n" +
                    "CREATE TABLE `legal_mediators` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '纠纷案件与调解员关联表主键',\n" +
                    "  `caseid` bigint(20) DEFAULT NULL COMMENT '案件ID',\n" +
                    "  `userid` bigint(20) DEFAULT NULL COMMENT '调解员ID',\n" +
                    "  `type` varchar(255) DEFAULT NULL COMMENT '类型 1：主办调解员；0：协办调解员',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='纠纷案件与调解员关联表';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `legal_case`;\n" +
                    "CREATE TABLE `legal_case` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '纠纷案件信息主键',\n" +
                    "  `ajbm` varchar(255) DEFAULT NULL COMMENT '案件编码',\n" +
                    "  `slrq` varchar(255) DEFAULT NULL COMMENT '受理时间',\n" +
                    "  `jflb` varchar(255) DEFAULT NULL COMMENT '纠纷类别',\n" +
                    "  `state` varchar(255) DEFAULT NULL COMMENT '纠纷阶段',\n" +
                    "  `ajndjb` varchar(255) DEFAULT NULL COMMENT '案件难度级别',\n" +
                    "  `JFZHQK` varchar(255) DEFAULT NULL COMMENT '纠纷转化情况',\n" +
                    "  `AJSX` varchar(255) DEFAULT NULL COMMENT '案件属性',\n" +
                    "  `tjsj` varchar(255) DEFAULT NULL COMMENT '调解时间',\n" +
                    "  `tjjg` varchar(255) DEFAULT NULL COMMENT '调解结果',\n" +
                    "  `xylxqk` varchar(255) DEFAULT NULL COMMENT '履行情况',\n" +
                    "  `jaTime` varchar(255) DEFAULT NULL COMMENT '结案时间',\n" +
                    "  `jfjyqk` varchar(255) DEFAULT NULL COMMENT '纠纷简要情况',\n" +
                    "  `twhid` bigint(20) DEFAULT NULL COMMENT '调委会ID',\n" +
                    "  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='纠纷案件信息';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `integral_set`;\n" +
                    "CREATE TABLE `integral_set` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '积分设置主键',\n" +
                    "  `key` varchar(255) DEFAULT NULL COMMENT '标志',\n" +
                    "  `score` varchar(255) DEFAULT NULL COMMENT '分值',\n" +
                    "  `remark` varchar(255) DEFAULT NULL COMMENT '备注',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='积分设置';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `integral_record`;\n" +
                    "CREATE TABLE `integral_record` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '积分记录主键',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='积分记录';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `integral`;\n" +
                    "CREATE TABLE `integral` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '积分主键',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='积分';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `declar_activities`;\n" +
                    "CREATE TABLE `declar_activities` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申报活动主键',\n" +
                    "  `declar_id` bigint(20) DEFAULT NULL COMMENT '申报ID',\n" +
                    "  `activity_theme` varchar(255) DEFAULT NULL COMMENT '活动主题',\n" +
                    "  `activity_remarks` varchar(255) DEFAULT NULL COMMENT '活动简介',\n" +
                    "  `service_category` varchar(255) DEFAULT NULL COMMENT '服务种类',\n" +
                    "  `service_items` varchar(255) DEFAULT NULL COMMENT '服务事项',\n" +
                    "  `activitie_start_time` datetime DEFAULT NULL COMMENT '活动开始时间',\n" +
                    "  `activity_end_time` datetime DEFAULT NULL COMMENT '活动开始时间',\n" +
                    "  `service_personnel_type` varchar(255) DEFAULT NULL COMMENT '服务人员类型',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='申报活动';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\n" +
                    "DROP TABLE IF EXISTS `declar`;\n" +
                    "CREATE TABLE `declar` (\n" +
                    "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申报主键',\n" +
                    "  `review_results` varchar(255) DEFAULT NULL COMMENT '审核结果',\n" +
                    "  `audit_opinion` varchar(255) DEFAULT NULL COMMENT '审核意见',\n" +
                    "  `result_id` bigint(20) DEFAULT NULL COMMENT '填报人ID',\n" +
                    "  `service_category` varchar(255) DEFAULT NULL COMMENT '服务种类',\n" +
                    "  `service_items` varchar(255) DEFAULT NULL COMMENT '服务事项',\n" +
                    "  `remarks` varchar(255) DEFAULT NULL COMMENT '工作简介',\n" +
                    "  `declare_att` varchar(255) DEFAULT NULL COMMENT '申报附件',\n" +
                    "  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（字典 0正常 1停用 2删除）',\n" +
                    "  `create_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '创建人',\n" +
                    "  `update_user` varchar(255) NOT NULL DEFAULT '-' COMMENT '最后更新人',\n" +
                    "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                    "  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,\n" +
                    "  PRIMARY KEY (`id`)\n" +
                    ") ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='申报';\n" +
                    "SET FOREIGN_KEY_CHECKS = 1;\n" +
                    "\nt":return "String";
            case "text":return "String";
            case "longtext":return "String";
            case "nchar":return "String";
            case "blob":return "byte[]";
            case "integer":return "Long";
            case "int":return "Integer";
            case "tinyint":return "Integer";
            case "smallint":return "Integer";
            case "mediumint":return "Integer";
            case "bit":return "Boolean";
            case "bigint":return "Long";
            case "number":return "Long";
            case "float":return "Float";
            case "double":return "Double";
            case "decimal":return "BigDecimal";
            case "boolean":return "Boolean";
            case "id":return "Long";
            case "date":return "Date";
            case "datetime":return "Date";
            case "year":return "Date";
            case "time":return "Time";
            case "timestamp":return "Timestamp";
            case "numeric":return "BigDecimal";
            case "real":return "BigDecimal";
            case "money":return "Double";
            case "smallmoney":return "Double";
            case "image":return "byte[]";
            default:
                System.out.println(">>> 转化失败：未发现的类型" + sqlType);
                break;
        }
        return sqlType;
    }
}
