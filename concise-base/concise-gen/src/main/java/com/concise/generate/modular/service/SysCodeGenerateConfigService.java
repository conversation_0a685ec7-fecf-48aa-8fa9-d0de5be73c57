package com.concise.generate.modular.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.generate.modular.entity.CodeGenerate;
import com.concise.generate.modular.entity.SysCodeGenerateConfig;
import com.concise.generate.modular.param.SysCodeGenerateConfigParam;
import com.concise.generate.modular.result.InforMationColumnsResult;

import java.util.List;

/**
 * 代码生成详细配置service接口
 *
 * <AUTHOR>
 * @date 2021-02-06 20:19:49
 */
public interface SysCodeGenerateConfigService extends IService<SysCodeGenerateConfig> {

    /**
     * 代码生成详细配置列表
     *
     * <AUTHOR>
     * @date 2021-02-06 20:19:49
     */
    List<SysCodeGenerateConfig> list(SysCodeGenerateConfigParam sysCodeGenerateConfigParam);

    /**
     * 添加代码生成详细配置
     *
     * <AUTHOR>
     * @date 2021-02-06 20:19:49
     */
    void add(SysCodeGenerateConfigParam sysCodeGenerateConfigParam);

    /**
     * 添加代码生成详细配置列表
     *
     * <AUTHOR>
     * @date 2021-02-06 20:19:49
     */
    void addList(List<InforMationColumnsResult> inforMationColumnsResultList, CodeGenerate codeGenerate);

    /**
     * 删除代码生成详细配置
     *
     * <AUTHOR>
     * @date 2021-02-06 20:19:49
     */
    void delete(SysCodeGenerateConfigParam sysCodeGenerateConfigParam);

    /**
     * 编辑代码生成详细配置
     *
     * <AUTHOR>
     * @date 2021-02-06 20:19:49
     */
    void edit(List<SysCodeGenerateConfigParam> sysCodeGenerateConfigParamList);

    /**
     * 查看代码生成详细配置
     *
     * <AUTHOR>
     * @date 2021-02-06 20:19:49
     */
     SysCodeGenerateConfig detail(SysCodeGenerateConfigParam sysCodeGenerateConfigParam);
}
