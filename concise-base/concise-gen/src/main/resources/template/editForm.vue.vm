<template>
  <a-modal
    title="编辑${functionName}"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
#foreach ($column in $tableField)
#if(${column.columnKey} == "PRI")
        <a-form-item v-show="false"><a-input v-decorator="['${column.javaName}']" /></a-form-item>
#else
#if (${column.whetherAddUpdate} == "Y")
#if (${column.effectType} == 'select' || ${column.effectType} == 'radio' || ${column.effectType} == 'checkbox')
        <a-form-item
          label="${column.columnComment}"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
#else
        <a-form-item
          label="${column.columnComment}"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
#end
#if (${column.effectType} == "user")
#if (${column.whetherRequired} == "Y")
          <user-select placeholder="请输入${column.columnComment}" v-decorator="['${column.javaName}', {rules: [{required: true, message: '请输入${column.columnComment}！'}]}]" />
#else
          <user-select placeholder="请输入${column.columnComment}" v-decorator="['${column.javaName}']" />
#end
#end
#if (${column.effectType} == "depart")
#if (${column.whetherRequired} == "Y")
          <depart-select placeholder="请输入${column.columnComment}" v-decorator="['${column.javaName}', {rules: [{required: true, message: '请输入${column.columnComment}！'}]}]" />
#else
          <depart-select placeholder="请输入${column.columnComment}" v-decorator="['${column.javaName}']" />
#end
#end
#if (${column.effectType} == "input")
#if (${column.whetherRequired} == "Y")
          <a-input placeholder="请输入${column.columnComment}" v-decorator="['${column.javaName}', {rules: [{required: true, message: '请输入${column.columnComment}！'}]}]" />
#else
          <a-input placeholder="请输入${column.columnComment}" v-decorator="['${column.javaName}']" />
#end
#end
#if (${column.effectType} == "textarea")
#if (${column.whetherRequired} == "Y")
          <a-textarea placeholder="请输入${column.columnComment}" v-decorator="['${column.javaName}', {rules: [{required: true, message: '请输入${column.columnComment}！'}]}]" :auto-size="{ minRows: 3, maxRows: 6 }"/>
#else
          <a-textarea placeholder="请输入${column.columnComment}" v-decorator="['${column.javaName}']" :auto-size="{ minRows: 3, maxRows: 6 }"/>
#end
#end
#if (${column.effectType} == "inputnumber")
#if (${column.whetherRequired} == "Y")
          <a-input-number placeholder="请输入${column.columnComment}" style="width: 100%" v-decorator="['${column.javaName}', {rules: [{required: true, message: '请输入${column.columnComment}！'}]}]" />
#else
          <a-input-number placeholder="请输入${column.columnComment}" style="width: 100%" v-decorator="['${column.javaName}']" />
#end
#end
#if (${column.effectType} == "select")
#if (${column.whetherRequired} == "Y")
          <a-select style="width: 100%" placeholder="请选择${column.columnComment}" v-decorator="['${column.javaName}', {rules: [{ required: true, message: '请选择${column.columnComment}！' }]}]">
            <a-select-option v-for="(item,index) in ${column.javaName}Data" :key="index" :value="item.code">{{ item.name }}</a-select-option>
          </a-select>
#else
          <a-select style="width: 100%" placeholder="请选择${column.columnComment}">
            <a-select-option v-for="(item,index) in ${column.javaName}Data" :key="index" :value="item.code">{{ item.name }}</a-select-option>
          </a-select>
#end
#end
#if (${column.effectType} == "radio")
#if (${column.whetherRequired} == "Y")
          <a-radio-group placeholder="请选择${column.columnComment}" v-decorator="['${column.javaName}',{rules: [{ required: true, message: '请选择${column.columnComment}！' }]}]" >
            <a-radio v-for="(item,index) in ${column.javaName}Data" :key="index" :value="item.code">{{ item.name }}</a-radio>
          </a-radio-group>
#else
          <a-radio-group placeholder="请选择${column.columnComment}" v-decorator="['${column.javaName}']" >
            <a-radio v-for="(item,index) in ${column.javaName}Data" :key="index" :value="item.code">{{ item.name }}</a-radio>
          </a-radio-group>
#end
#end
#if (${column.effectType} == "checkbox")
#if (${column.whetherRequired} == "Y")
          <a-checkbox-group placeholder="请选择${column.columnComment}" v-decorator="['${column.javaName}',{rules: [{ required: true, message: '请选择${column.columnComment}！' }]}]" >
            <a-checkbox v-for="(item,index) in ${column.javaName}Data" :key="index" :value="item.code">{{ item.name }}</a-checkbox>
          </a-checkbox-group>
#else
          <a-checkbox-group placeholder="请选择${column.columnComment}" v-decorator="['${column.javaName}']" >
            <a-checkbox v-for="(item,index) in ${column.javaName}Data" :key="index" :value="item.code">{{ item.name }}</a-checkbox>
          </a-checkbox-group>
#end
#end
#if (${column.effectType} == "datepicker")
#if (${column.whetherRequired} == "Y")
          <a-date-picker style="width: 100%" placeholder="请选择${column.columnComment}" v-decorator="['${column.javaName}',{rules: [{ required: true, message: '请选择${column.columnComment}！' }]}]" @change="onChange${column.javaName}"/>
#else
          <a-date-picker style="width: 100%" placeholder="请选择${column.columnComment}" v-decorator="['${column.javaName}']" @change="onChange${column.javaName}"/>
#end
#end
        </a-form-item>
#end
#end
#end
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
#set ($editData = 0)
#foreach ($column in $tableField)
#if (${column.whetherAddUpdate} == "Y")
#if (${column.effectType} == "datepicker")
#set ($DateQuery="Y")
#end
#if(${column.columnKey} != "PRI")
#if(${column.effectType} != "datepicker")
#set ($editData = $editData+1)
#end
#end
#end
#end
#if($DateQuery == "Y")
  import moment from 'moment'
#end
  import { ${className}Edit } from '@/api/modular/main/${busName}/${className}Manage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
#foreach ($column in $tableField)
#if (${column.whetherAddUpdate} == "Y")
#if (${column.dictTypeCode})
#if(${column.effectType} == 'select' || ${column.effectType} == 'radio' || ${column.effectType} == 'checkbox')
        ${column.javaName}Data: [],
#end
#end
#if (${column.effectType} == "datepicker")
        ${column.javaName}DateString: '',
#end
#end
#end
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
#if($DateQuery == "Y")
      moment,
#end
      // 初始化方法
      edit (record) {
        this.visible = true
#foreach ($column in $tableField)
#if (${column.dictTypeCode})
#if(${column.effectType} == 'select' || ${column.effectType} == 'radio' || ${column.effectType} == 'checkbox')
        const ${column.javaName}Option = this.$options
        this.${column.javaName}Data = ${column.javaName}Option.filters['dictData']('${column.dictTypeCode}')
#end
#end
#end
        setTimeout(() => {
          this.form.setFieldsValue(
            {
#set ($editDataColumn = 0)
#foreach ($column in $tableField)
#if(${column.columnKey} == "PRI")
              ${column.javaName}: record.${column.javaName}#if($foreach.hasNext),
#end
#else
#if (${column.whetherAddUpdate} == "Y")
#if (${column.effectType} == "checkbox")
#set ($editDataColumn = $editDataColumn+1)
              ${column.javaName}: JSON.parse(record.${column.javaName})#if($foreach.hasNext && ($editDataColumn != $editData)),
#else

#end
#elseif (${column.effectType} != "datepicker")
#set ($editDataColumn = $editDataColumn+1)
              ${column.javaName}: record.${column.javaName}#if($foreach.hasNext && ($editDataColumn != $editData)),
#else

#end
#end
#end
#end
#end
            }
          )
        }, 100)
#foreach ($column in $tableField)
#if (${column.whetherAddUpdate} == "Y")
#if (${column.effectType} == "datepicker")
        // 时间单独处理
        if (record.${column.javaName} != null) {
            this.form.getFieldDecorator('${column.javaName}', { initialValue: moment(record.${column.javaName}, 'YYYY-MM-DD') })
        }
        this.${column.javaName}DateString = moment(record.${column.javaName}).format('YYYY-MM-DD')
#end
#end
#end
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
#foreach ($column in $tableField)
#if (${column.whetherAddUpdate} == "Y")
#if (${column.effectType} == "datepicker")
            values.${column.javaName} = this.${column.javaName}DateString
#end
#end
#end
            ${className}Edit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
#foreach ($column in $tableField)
#if (${column.whetherAddUpdate} == "Y")
#if (${column.effectType} == "datepicker")
      onChange${column.javaName}(date, dateString) {
        this.${column.javaName}DateString = dateString
      },
#end
#end
#end
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
