package ${packageName}.${modularName}.${busName}.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.concise.common.pojo.base.entity.BaseEntity;
#foreach ($column in $tableField)
#if (${column.javaType} == 'BigDecimal')
#end
#if(${column.javaType} == "Date")
import cn.afterturn.easypoi.excel.annotation.Excel;
#end
#end

/**
 * ${functionName}
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("${tableName}")
public class ${ClassName} extends BaseEntity {

#foreach ($column in $tableField)
#if (${column.columnKey} == "PRI")
    /**
     * ${column.columnComment}
     */
    @TableId(type = IdType.ASSIGN_ID)
    private ${column.javaType} ${column.javaName};
#elseif (${column.whetherCommon} == 'N')

    /**
     * ${column.columnComment}
     */
#if(${column.javaType} == "Date")
    @Excel(name = "${column.columnComment}", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
#end
#if(${column.javaType} == "longtext")
    private String ${column.javaName};
#else
    private ${column.javaType} ${column.javaName};
#end
#end
#end

}
