package ${packageName}.${modularName}.${busName}.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
#foreach ($column in $tableField)
#if (${column.javaType} == 'BigDecimal')
#end
#end

/**
* ${functionName}参数类
 *
 * <AUTHOR>
 * @date ${createDateString}
*/
@Data
public class ${ClassName}Param extends BaseParam {
#foreach ($column in $tableField)
#if (${column.columnKey} == "PRI")

    /**
     * ${column.columnComment}
     */
    @ApiModelProperty(value = "${column.columnComment}")
    @NotNull(message = "${column.columnComment}不能为空，请检查${column.javaName}参数", groups = {edit.class, delete.class, detail.class})
    private ${column.javaType} ${column.javaName};
#elseif (${column.whetherCommon} == 'N')

    /**
     * ${column.columnComment}
     */
    @ApiModelProperty(value = "${column.columnComment}")
#if (${column.whetherRequired} == "Y")
#if (${column.javaType} == "String")
    @NotBlank(message = "${column.columnComment}不能为空，请检查${column.javaName}参数", groups = {add.class, edit.class})
#else
    @NotNull(message = "${column.columnComment}不能为空，请检查${column.javaName}参数", groups = {add.class, edit.class})
#end
#end
#if(${column.javaType} == "Date")
    private String ${column.javaName};
#else
#if(${column.javaType} == "longtext")
    private String ${column.javaName};
#else
    private ${column.javaType} ${column.javaName};
#end
#end
#end
#end

}
