import { axios } from '@/utils/request'

/**
 * 查询${functionName}
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
export function ${className}Page (parameter) {
  return axios({
    url: '/${className}/page',
    method: 'get',
    params: parameter
  })
}

/**
 * ${functionName}列表
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
export function ${className}List (parameter) {
  return axios({
    url: '/${className}/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加${functionName}
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
export function ${className}Add (parameter) {
  return axios({
    url: '/${className}/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑${functionName}
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
export function ${className}Edit (parameter) {
  return axios({
    url: '/${className}/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除${functionName}
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
export function ${className}Delete (parameter) {
  return axios({
    url: '/${className}/delete',
    method: 'post',
    data: parameter
  })
}
