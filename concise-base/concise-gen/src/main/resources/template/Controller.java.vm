package ${packageName}.${modularName}.${busName}. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import ${packageName}.${modularName}.${busName}. param.${ClassName}Param;
import ${packageName}.${modularName}.${busName}. service.${ClassName}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * ${functionName}控制器
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
@Api(tags = "${functionName}")
@RestController
public class ${ClassName}Controller {

    @Resource
    private ${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @GetMapping("/${className}/page")
    @ApiOperation("${functionName}_分页查询")
    @BusinessLog(title = "${functionName}_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(${ClassName}Param ${className}Param) {
        return new SuccessResponseData(${className}Service.page(${className}Param));
    }

    /**
     * 添加${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @PostMapping("/${className}/add")
    @ApiOperation("${functionName}_增加")
    @BusinessLog(title = "${functionName}_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(${ClassName}Param.add.class) ${ClassName}Param ${className}Param) {
        ${className}Service.add(${className}Param);
        return new SuccessResponseData();
    }

    /**
     * 删除${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @PostMapping("/${className}/delete")
    @ApiOperation("${functionName}_删除")
    @BusinessLog(title = "${functionName}_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(${ClassName}Param.delete.class) ${ClassName}Param ${className}Param) {
        ${className}Service.delete(${className}Param);
        return new SuccessResponseData();
    }

    /**
     * 编辑${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @PostMapping("/${className}/edit")
    @ApiOperation("${functionName}_编辑")
    @BusinessLog(title = "${functionName}_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(${ClassName}Param.edit.class) ${ClassName}Param ${className}Param) {
        ${className}Service.edit(${className}Param);
        return new SuccessResponseData();
    }

    /**
     * 查看${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @GetMapping("/${className}/detail")
    @ApiOperation("${functionName}_查看")
    @BusinessLog(title = "${functionName}_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(${ClassName}Param.detail.class) ${ClassName}Param ${className}Param) {
        return new SuccessResponseData(${className}Service.detail(${className}Param));
    }

    /**
     * ${functionName}列表
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    @Permission
    @GetMapping("/${className}/list")
    @ApiOperation("${functionName}_列表")
    @BusinessLog(title = "${functionName}_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(${ClassName}Param ${className}Param) {
        return new SuccessResponseData(${className}Service.list(${className}Param));
    }

}
