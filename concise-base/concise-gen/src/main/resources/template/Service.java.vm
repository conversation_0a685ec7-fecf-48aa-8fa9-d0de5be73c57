package ${packageName}.${modularName}.${busName}.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import ${packageName}.${modularName}.${busName}.entity.${ClassName};
import ${packageName}.${modularName}.${busName}.param.${ClassName}Param;
import java.util.List;

/**
 * ${functionName}service接口
 *
 * <AUTHOR>
 * @date ${createDateString}
 */
public interface ${ClassName}Service extends IService<${ClassName}> {

    /**
     * 查询${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    PageResult<${ClassName}> page(${ClassName}Param ${className}Param);

    /**
     * ${functionName}列表
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    List<${ClassName}> list(${ClassName}Param ${className}Param);

    /**
     * 添加${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    void add(${ClassName}Param ${className}Param);

    /**
     * 删除${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    void delete(${ClassName}Param ${className}Param);

    /**
     * 编辑${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
    void edit(${ClassName}Param ${className}Param);

    /**
     * 查看${functionName}
     *
     * <AUTHOR>
     * @date ${createDateString}
     */
     ${ClassName} detail(${ClassName}Param ${className}Param);
}
