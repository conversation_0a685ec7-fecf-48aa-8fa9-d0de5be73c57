package com.concise.core.validation.datetime;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验日期时间格式 yyyy-MM-dd HH:mm:ss
 *
 * <AUTHOR>
 * @date 2020/5/26 14:48
 */
@Documented
@Constraint(validatedBy = DateTimeValueValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface DateTimeValue {

    String message() default "日期时间格式不正确，正确格式应为yyyy-MM-dd HH:mm:ss";

    Class[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RUNTIME)
    @Documented
    @interface List {
        DateTimeValue[] value();
    }
}
