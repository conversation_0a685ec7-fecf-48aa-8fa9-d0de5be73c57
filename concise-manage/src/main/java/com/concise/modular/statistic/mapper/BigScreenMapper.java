package com.concise.modular.statistic.mapper;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.modular.evaluation.examresult.entity.ExamResult;
import com.concise.modular.law.lawposition.entity.LawPosition;
import com.concise.modular.statistic.vo.LawUserVo;
import com.concise.modular.statistic.vo.ResourceVo;
import com.concise.modular.statistic.vo.ScreenVo;

/**
 * <AUTHOR>
 * @date 2024/9/25
 */
public interface BigScreenMapper {


    List<ScreenVo> lawCultivateSex(@Param("areaCode") String areaCode);

    ScreenVo lawCultivateArea(@Param("level") Integer level);

    ScreenVo lawCultivateAreaByOrgId(@Param("level") Integer level, @Param("orgId") String orgId);


    List<ScreenVo> lawLearnerPolitical(@Param("areaCode") String areaCode);

    List<ScreenVo> lawLearnerEducation(@Param("areaCode") String areaCode);

    List<ScreenVo> lawLearnerPersonType(@Param("areaCode") String areaCode);

    Page<LawUserVo> lawCultivateUser(@Param("page") Page page, @Param("code") Integer code, @Param("orgIds") Set<String> orgIds);

    List<ScreenVo> lawResponsibility();

    ExamResult threeDimensional(@Param("areaName") String areaName);

    ExamResult fiveLaw(@Param("areaName") String areaName);

    List<ScreenVo> lawFunds(@Param("year") int year);


    List<ScreenVo> lawFundsArea(@Param("year") int year, @Param("areaName") String areaName);

    List<ResourceVo> lawResource(@Param("deptIds") Set<String> deptIds);

    List<ResourceVo> lawResourceDetail(@Param("deptIds") Set<String> deptIds);

    List<JSONObject> pointInfo(@Param("name") String name, @Param("deptIds") Set<String> ids, @Param("areaCode") String areaCode);

    List<JSONObject> pointInfoMetrics(@Param("name") String name);


    List<ScreenVo> pointCount();

    List<JSONObject> lawPositionDetail(@Param("city") String city);

    List<JSONObject> lawPositionDetailNum(@Param("type") Integer type);

    List<JSONObject> obPointCount();

    List<JSONObject> lawPositionDetailNumArea(@Param("areaName") String areaName, @Param("type") Integer type);

    List<JSONObject> obPointCountArea(@Param("areaName") String areaName);

    List<JSONObject> lawEducationCityDistribution();

    List<String> lawEducationCityDistributionKeyword(@Param("city") String city);

    List<JSONObject> lawEducationVersionCount();

    List<JSONObject> lawPositionDetailArea(@Param("areaName") String areaName);

    List<JSONObject> obPointCountAreaQx(@Param("areaName") String areaName);

    List<JSONObject> lawPositionDetailNumAreaQx(@Param("areaName") String areaName, @Param("type") Integer type);

    ResourceVo lawResourceFourType(@Param("deptIds") Set<String> deptIds);

    int lawLearnerTotal(@Param("areaCode") String areaCode);

    List<JSONObject> lawEducationCityDistributionCity(@Param("areaCode") String areaCode);

    List<JSONObject> lawEducationCityDistributionQx(@Param("areaCode") String areaCode);

    List<JSONObject> lawEducationVersionCountCity(@Param("areaCode") String areaCode);

    List<JSONObject> lawEducationVersionCountQx(@Param("areaCode") String areaCode);

    List<String> lawEducationCityDistributionKeywordQx(@Param("city") String city);

    List<LawPosition> villageList(@Param("areaCode") String areaCode);

    List<JSONObject> lawReport();

    List<JSONObject> lawReportCity(@Param("areaName") String areaName);

    List<JSONObject> lawReportQx(@Param("areaName") String areaName);

    Integer villageTotal(@Param("areaCode") String areaCode);

    List<JSONObject> villageTotalCity(@Param("areaCode") String areaCode);

    /**
     * 获取测评参与人数
     *
     * @param areaCode 区域编码
     * @return 参与人数
     */
    int examTestCount(@Param("areaCode") String areaCode);

    /**
     * 获取测评参与人数（使用区域代码列表）
     *
     * @param areaCodes 区域编码列表
     * @return 参与人数
     */
    int examTestCountByAreaCodes(@Param("areaCodes") Set<String> areaCodes);

    /**
     * 获取测评参与人数（使用区域名称列表）
     *
     * @param areaNames 区域名称列表
     * @return 参与人数
     */
    int examTestCountByAreaNames(@Param("areaNames") Set<String> areaNames);

    /**
     * 获取基准点自测参与人数
     *
     * @param areaCode 区域编码
     * @return 参与人数
     */
    int benchmarkTestCount(@Param("areaCode") String areaCode);

    /**
     * 获取基准点自测参与人数（使用区域代码列表）
     *
     * @param areaCodes 区域编码列表
     * @return 参与人数
     */
    int benchmarkTestCountByAreaCodes(@Param("areaCodes") Set<String> areaCodes);

    /**
     * 分页查询测评详细数据（使用区域代码列表）
     *
     * @param type      测评类型 1-公民法治素养测评 其他-基准点自测
     * @param areaCodes 区域编码列表
     * @param areaNames 区域名称列表
     * @return 测评详细数据
     */
    List<JSONObject> lawTestPageByAreaCodes(@Param("type") Integer type, @Param("areaCodes") Set<String> areaCodes, @Param("areaNames") Set<String> areaNames);

    /**
     * 获取各地市法律活动数量统计
     *
     * @param areaCode 区域编码
     * @return 地市活动统计列表
     */
    List<Map<String, Object>> getCityActivityStatistics(@Param("areaCode") String areaCode);

    /**
     * 各地市普法队伍人数
     */
    List<JSONObject> lawCultivateAnalyseProvince();

    /**
     * 各地市下的区县普法队伍人数
     */
    List<JSONObject> lawCultivateAnalyseCity(@Param("areaName") String areaName);

    /**
     * 区县普法队伍人数
     */
    List<JSONObject> lawCultivateAnalyseArea(@Param("areaName") String areaName);

    List<JSONObject> lawUnderstandProvince();

    List<JSONObject> lawUnderstandCity(@Param("areaName") String areaName);

    List<JSONObject> lawUnderstandArea(@Param("areaName") String areaName);

    /**
     * 获取普法干部用户列表
     *
     * @param areaCode 区域编码
     * @return 普法干部用户列表
     */
    List<LawUserVo> lawCultivateUserList(@Param("areaCode") String areaCode);

    /**
     * 获取法律明白人用户列表
     *
     * @param areaCode 区域编码
     * @return 法律明白人用户列表
     */
    List<LawUserVo> lawLearnerUserList(@Param("areaCode") String areaCode);

    /**
     * 获取普法干部用户列表（带分页）
     *
     * @param page     分页参数
     * @param areaCode 区域编码
     * @return 普法干部用户列表
     */
    Page<LawUserVo> lawCultivateUserPageWithPagination(@Param("page") Page<LawUserVo> page, @Param("areaCode") String areaCode);

    /**
     * 获取法律明白人用户列表（带分页）
     *
     * @param page     分页参数
     * @param areaCode 区域编码
     * @return 法律明白人用户列表
     */
    Page<LawUserVo> lawLearnerUserPageWithPagination(@Param("page") Page<LawUserVo> page, @Param("areaCode") String areaCode);

    /**
     * 民主法治示范村分页查询
     *
     * @param areaName 区域名称
     * @param level    级别（1-国家级，2-省级）
     * @return 查询结果列表
     */
    List<JSONObject> lawVillageTotalPage(@Param("areaName") String areaName, @Param("level") Integer level);

    /**
     * 民主法治示范村分页查询（带分页）
     *
     * @param page     分页参数
     * @param areaName 区域名称
     * @param level    级别（1-国家级，2-省级）
     * @return 查询结果列表
     */
    Page<JSONObject> lawVillageTotalPageWithPagination(@Param("page") Page<JSONObject> page, @Param("areaName") String areaName, @Param("level") Integer level);

    /**
     * 分页查询测评详细数据
     *
     * @param page     分页参数
     * @param type     测评类型：1-公民法治素养测评，2-公民法治素养基准点自测
     * @param areaCode 区域编码
     * @return 测评详细数据
     */
    List<JSONObject> lawTestPage(@Param("page") Page page, @Param("type") Integer type, @Param("areaCode") String areaCode);

    /**
     * 分页查询测评详细数据（带分页）
     *
     * @param page     分页参数
     * @param type     测评类型：1-公民法治素养测评，2-公民法治素养基准点自测
     * @param areaCode 区域编码
     * @return 测评详细数据
     */
    Page<JSONObject> lawTestPageWithPagination(@Param("page") Page<JSONObject> page, @Param("type") Integer type, @Param("areaCode") String areaCode);

    /**
     * 分页查询测评详细数据（使用区域代码列表，带分页）
     *
     * @param page      分页参数
     * @param type      测评类型：1-公民法治素养测评，2-公民法治素养基准点自测
     * @param areaCodes 区域编码列表
     * @param areaNames 区域名称列表
     * @return 测评详细数据
     */
    Page<JSONObject> lawTestPageWithPaginationByAreaCodes(@Param("page") Page<JSONObject> page, @Param("type") Integer type, @Param("areaCodes") Set<String> areaCodes, @Param("areaNames") Set<String> areaNames);

    /**
     * 分页查询法治文化阵地数据
     *
     * @param page          分页参数
     * @param type          阵地类型：7-中华优秀传统法律文化阵地，8-红色法治文化阵地，9-宪法宣传教育阵地，10-民法典宣传教育阵地，11-青少年法治宣传教育阵地，12-其他地方特色法治文化阵地
     * @param positionLevel 阵地级别：1-全国级，2-省级
     * @param areaName      区域名称
     * @return 法治文化阵地数据
     */
    List<JSONObject> lawCulturePage(@Param("page") Page page, @Param("type") Integer type, @Param("positionLevel") Integer positionLevel, @Param("areaName") String areaName);

    /**
     * 分页查询法治文化阵地数据（带分页）
     *
     * @param page          分页参数
     * @param type          阵地类型：7-中华优秀传统法律文化阵地，8-红色法治文化阵地，9-宪法宣传教育阵地，10-民法典宣传教育阵地，11-青少年法治宣传教育阵地，12-其他地方特色法治文化阵地
     * @param positionLevel 阵地级别：1-全国级，2-省级
     * @param areaName      区域名称
     * @return 法治文化阵地数据
     */
    Page<JSONObject> lawCulturePageWithPagination(@Param("page") Page<JSONObject> page, @Param("type") Integer type, @Param("positionLevel") Integer positionLevel, @Param("areaName") String areaName);

    /**
     * 分页查询法治文艺资源数据
     *
     * @param page     分页参数
     * @param type     资源类型：11-漫画，12-宣传画，13-海报，31-动画，32-宣传片，33-微电影，34-情景剧，41-视频课件，42-文档课件，1-公益广告
     * @param areaName 区域名称
     * @return 法治文艺资源数据
     */
    Page<JSONObject> lawArtPage(@Param("page") Page<JSONObject> page, @Param("type") String type, @Param("areaName") String areaName);

    /**
     * 分页查询法治文艺主题分类资源数据
     *
     * @param page      分页参数
     * @param labelName 主题标签名称
     * @param areaName  区域名称
     * @return 法治文艺主题分类资源数据
     */
    Page<JSONObject> lawArtCategoryPage(@Param("page") Page<JSONObject> page, @Param("labelName") String labelName, @Param("areaName") String areaName);

    /**
     * 按地区统计主题宣传月资源数量
     *
     * @param labelPid 主题宣传月标签ID
     * @param areaName 区域名称
     * @return 主题宣传月资源统计
     */
    List<JSONObject> lawThemeMonthStatistics(@Param("labelPid") String labelPid, @Param("areaName") String areaName);

    /**
     * 分页查询主题宣传月资源数据
     *
     * @param page     分页参数
     * @param labelPid 主题宣传月标签ID
     * @param areaName 区域名称
     * @return 主题宣传月资源数据
     */
    List<JSONObject> lawThemeMonthPage(@Param("page") Page page, @Param("labelPid") String labelPid, @Param("areaName") String areaName);

    /**
     * 分页查询主题宣传月资源数据（带分页）
     *
     * @param page     分页参数
     * @param labelPid 主题宣传月标签ID
     * @param areaName 区域名称
     * @return 主题宣传月资源数据
     */
    Page<JSONObject> lawThemeMonthPageWithPagination(@Param("page") Page<JSONObject> page, @Param("labelPid") String labelPid, @Param("areaName") String areaName);

    /**
     * 普法活动主题统计
     *
     * @return 活动主题统计
     */
    List<JSONObject> lawActivityTheme();

    /**
     * 普法活动趋势统计（近12个月）
     *
     * @return 活动趋势统计
     */
    List<JSONObject> lawActivityTrend();

    /**
     * 普法活动风采（最新10个带图文的活动）
     *
     * @return 活动风采列表
     */
    List<JSONObject> lawActivityStyle();

    /**
     * 普法活动列表（分页）
     *
     * @param page 分页参数
     * @return 活动列表
     */
    Page<JSONObject> lawActivityList(@Param("page") Page<JSONObject> page);

    /**
     * 普法活动趋势统计 按单位（近12个月）
     *
     * @return 活动趋势统计 按单位
     */
    List<JSONObject> lawActivityTrendOrg();

    /**
     * 法治文化主题月视频
     *
     * @param areaName
     * @return
     */
    List<JSONObject> lawThemeMonthVideo(@Param("labelPid") String labelPid, @Param("areaName") String areaName);
}
