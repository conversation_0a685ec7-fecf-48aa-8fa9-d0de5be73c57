package com.concise.modular.statistic.service;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.benchmark.benchmarkmanage.entity.BenchmarkManage;
import com.concise.modular.law.lawposition.entity.LawPosition;
import com.concise.modular.obpoint.entity.ObPointInfo;

/**
 * 新版大屏服务接口
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
public interface NewBigScreenService {

    /**
     * 全国民主法治示范村列表
     *
     * @param areaName 区域名称
     * @return 示范村列表
     */
    Map<String, Object> villageList(String areaName);

    /**
     * 全国民主法治示范村数量统计
     *
     * @param areaName 区域名称
     * @return 数量统计
     */
    Map<String, Object> villageTotal(String areaName);

    /**
     * 根据名称搜索民主法治村
     *
     * @param name 村名称
     * @return 搜索结果
     */
    List<LawPosition> villageSearch(String name);

    /**
     * 法治文化基地列表
     *
     * @param areaName 区域名称
     * @return 基地列表
     */
    List<JSONObject> cultureList(String areaName);

    /**
     * 法治文化基地数量统计
     *
     * @param areaName 区域名称
     * @return 数量统计
     */
    Map<String, Object> cultureTotal(String areaName,Integer type);

    /**
     * 法治文化基地按照区域统计下级数量和提供列表
     *
     * @param areaName 区域名称
     * @param type 类型
     * @return 统计结果
     */
    Map<String, Object> cultureDetail(String areaName, Integer type);

    /**
     * 法治文化基地按照区域和名称和类型来搜索
     *
     * @param areaName 区域名称
     * @param name 名称
     * @param type 类型
     * @param positionLevel 阵地级别
     * @return 搜索结果
     */
    List<JSONObject> cultureSearch(String areaName, String name, Integer type, Integer positionLevel);

    /**
     * 普法信息报送统计
     *
     * @param areaName 区域名称
     * @return 统计结果
     */
    List<JSONObject> lawReport(String areaName);

    /**
     * 普法信息报送统计-分页
     *
     * @param areaName 区域名称
     * @return 分页结果
     */
    PageResult<JSONObject> lawReportPage(String areaName);

    /**
     * 普法宣传-浙里普法访问人次
     *
     * @param areaName 区域名称
     * @param year 年份
     * @return 访问统计
     */
    List<JSONObject> lawVisit(String areaName, Integer year);

    /**
     * 普法宣传-群众学法
     *
     * @param areaName 区域名称
     * @return 学法统计
     */
    Map<String, Object> lawLearn(String areaName);

    /**
     * 普法宣传-群众学法-分页
     *
     * @param areaName 区域名称
     * @return 分页结果
     */
    PageResult<JSONObject> lawLearnPage(String areaName);

    /**
     * 普法宣传-普法评测
     *
     * @param areaName 区域名称
     * @return 评测统计
     */
    Map<String, Object> lawTest(String areaName);

    /**
     * 普法宣传-普法评测-分页
     *
     * @param areaName 区域名称
     * @param type 类型
     * @return 分页结果
     */
    PageResult<JSONObject> lawTestPage(String areaName, Integer type);

    /**
     * 普法宣传-浙里普法资讯统计
     *
     * @param areaName 区域名称
     * @return 资讯统计
     */
    Map<String, Object> lawNews(String areaName);

    /**
     * 普法宣传-浙里普法资讯统计-分页
     *
     * @param areaName 区域名称
     * @param labelName 标签名称
     * @return 分页结果
     */
    PageResult<JSONObject> lawNewsPage(String areaName, String labelName);

    /**
     * 普法宣传-普法热度洞察
     *
     * @param areaName 区域名称
     * @return 热度统计
     */
    Map<String, Object> lawHot(String areaName);

    /**
     * 普法宣传-按照区域查询信息报送统计
     *
     * @param areaName 区域名称
     * @return 统计结果
     */
    Map<String, Object> lawReportByArea(String areaName);

    /**
     * 法治文化-法治文化阵地
     *
     * @param areaName 区域名称
     * @return 阵地统计
     */
    List<JSONObject> lawCulture(String areaName);

    /**
     * 法治文化-法治文化阵地-分页
     *
     * @param areaName 区域名称
     * @param type 阵地类型
     * @param positionLevel 阵地级别
     * @return 分页结果
     */
    PageResult<JSONObject> lawCulturePage(String areaName, Integer type, Integer positionLevel);

    /**
     * 法治文化-法治文化阵地-地图点位展示
     *
     * @param areaName 区域名称
     * @param type 阵地类型
     * @param positionLevel 阵地级别
     * @return 地图点位数据
     */
    Map<String, Object> lawCultureMap(String areaName, Integer type, Integer positionLevel);

    /**
     * 法治文化-各地法治文化阵地
     *
     * @param areaName 区域名称
     * @return 阵地统计
     */
    List<JSONObject> lawCultureByArea(String areaName);

    /**
     * 法治文化-各地法治文化阵地列表
     *
     * @param areaName 区域名称
     * @return 阵地列表
     */
    List<JSONObject> lawCultureList(String areaName);

    /**
     * 法治文化-各地法治文化阵地列表-分页
     *
     * @param areaName 区域名称
     * @param level 阵地级别
     * @return 分页结果
     */
    PageResult<JSONObject> lawCultureListPage(String areaName, Integer level);

    /**
     * 法治文化-各地市普法活动总量统计
     *
     * @param areaName 区域名称
     * @return 活动统计
     */
    List<JSONObject> lawActivityTotal(String areaName);

    /**
     * 法治文化-法治文艺库
     *
     * @param areaName 区域名称
     * @return 文艺库统计
     */
    List<JSONObject> lawArt(String areaName);

    /**
     * 法治文化-法治文艺库-分页
     *
     * @param areaName 区域名称
     * @param type 文艺类型
     * @return 分页结果
     */
    PageResult<JSONObject> lawArtPage(String areaName, Integer type);

    /**
     * 法治文化-文艺库主题分类
     *
     * @param areaName 区域名称
     * @return 主题分类统计
     */
    List<JSONObject> lawArtCategory(String areaName);

    /**
     * 法治文化-文艺库主题分类-分页
     *
     * @param areaName 区域名称
     * @param labelName 主题标签名称
     * @return 分页结果
     */
    PageResult<JSONObject> lawArtCategoryPage(String areaName, String labelName);

    /**
     * 法治文化-文艺库面相群体分类
     *
     * @param areaName 区域名称
     * @return 群体分类统计
     */
    List<JSONObject> lawArtGroup(String areaName);

    /**
     * 随机给资源绑定标签
     */
    void lawResourceTag();

    /**
     * 依法治理-普法队伍统计分析
     *
     * @param areaName 区域名称
     * @return 统计分析结果
     */
    Map<String, Object> lawCultivateAnalyse(String areaName);

    /**
     * 依法治理-普法队伍统计分析-各地市区县分布
     *
     * @param areaName 区域名称
     * @param type 类型：1-普法干部 2-法律明白人 3-普法志愿者
     * @return 分布统计
     */
    List<JSONObject> lawCultivateAnalyseArea(String areaName, Integer type);

    /**
     * 依法治理-各地民主法治示范村
     *
     * @param areaName 区域名称
     * @return 示范村统计
     */
    Map<String, Object> lawVillage(String areaName);

    /**
     * 依法治理-各地民主法治示范村-总数统计和下级行政单位数量统计
     *
     * @param areaName 区域名称
     * @return 统计结果
     */
    Map<String, Object> lawVillageTotal(String areaName);

    /**
     * 依法治理-公民法治素养观测点
     *
     * @param areaName 区域名称
     * @return 观测点统计
     */
    Map<String, Object> lawObservatory(String areaName);

    /**
     * 依法治理-普法队伍统计分析-详情分页
     *
     * @param areaName 区域名称
     * @param type 类型
     * @return 分页结果
     */
    PageResult<?> lawCultivateAnalysePage(String areaName, Integer type);

    /**
     * 依法治理-普法队伍统计分析-各地市区县分布-分页
     *
     * @param areaName 区域名称
     * @param type 类型
     * @return 分页结果
     */
    PageResult<JSONObject> lawCultivateAnalyseAreaPage(String areaName, Integer type);

    /**
     * 依法治理-普法队伍统计分析-各地市区县分布-人员详情分页
     *
     * @param areaName 区域名称
     * @param type 类型
     * @return 分页结果
     */
    PageResult<JSONObject> lawCultivateAnalyseAreaUserPage(String areaName, Integer type);

    /**
     * 依法治理-各地民主法治示范村-详情分页
     *
     * @param areaName 区域名称
     * @return 分页结果
     */
    PageResult<JSONObject> lawVillagePage(String areaName);

    /**
     * 依法治理-各地民主法治示范村-总数统计分页
     *
     * @param areaName 区域名称
     * @param level 级别
     * @return 分页结果
     */
    PageResult<JSONObject> lawVillageTotalPage(String areaName, Integer level);

    /**
     * 依法治理-公民法治素养基准分页
     *
     * @param areaName 区域名称
     * @param type 类型
     * @return 分页结果
     */
    PageResult<BenchmarkManage> citizenLegalLiteracyPage(String areaName, Integer type);

    /**
     * 依法治理-公民法治素养观测点-详情分页
     *
     * @param areaName 区域名称
     * @param level 级别
     * @return 分页结果
     */
    PageResult<ObPointInfo> lawObservatoryPage(String areaName, Integer level);

    /**
     * 法治文化-主题宣传月统计
     *
     * @param areaName 区域名称
     * @return 主题宣传月资源统计
     */
    List<JSONObject> lawThemeMonth(String areaName);

    /**
     * 法治文化-主题宣传月-分页
     *
     * @param areaName 区域名称
     * @return 分页结果
     */
    PageResult<JSONObject> lawThemeMonthPage(String areaName);

    /**
     * 获取法律明白人总数
     *
     * @param areaName 区域名称
     * @return 法律明白人总数
     */
    int lawLearnerTotal(String areaName);

    /**
     * 依法治理-民主法治示范村(社区)特色
     *
     * @param areaName 区域名称
     * @return 特色列表
     */
    List<LawPosition> lawVillageFeature(String areaName);

    /**
     * 随机造主题宣传月活动数据
     */
    void lawThemeMonthData();

    /**
     * 普法活动主题
     *
     * @return 活动主题
     */
    List<JSONObject> lawActivityTheme();

    /**
     * 普法活动趋势
     *
     * @return 活动趋势
     */
    List<JSONObject> lawActivityTrend();

    /**
     * 普法活动风采
     *
     * @return 活动风采
     */
    List<JSONObject> lawActivityStyle();

    /**
     * 普法活动列表
     *
     * @return 活动列表
     */
    Map<String, Object> lawActivityList();

    /**
     * 法治文化-主题宣传月-视频轮播
     *
     * @param areaName 区域名称
     * @return 视频轮播
     */
    List<JSONObject> lawThemeMonthVideo(String areaName);
}
