package com.concise.modular.statistic.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.consts.CommonConstant;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.law.lawposition.service.LawPositionService;
import com.concise.modular.statistic.service.NewBigScreenService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 新版驾驶舱控制器
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Slf4j
@Api(tags = "新版驾驶舱")
@RestController
@RequestMapping("/v2")
public class NewBigScreenController {

    @Resource
    private NewBigScreenService newBigScreenService;

    @Resource
    private LawPositionService lawPositionService;


    /**
     * 全国民主法治示范村列表
     */
    @ApiOperation("全国民主法治示范村列表")
    @GetMapping("/bigScreen/villageList")
    public ResponseData villageList(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.villageList(areaName));
    }

    /**
     * 全国民主法治示范村数量统计
     */
    @ApiOperation("全国民主法治示范村数量统计")
    @GetMapping("/bigScreen/villageTotal")
    public ResponseData villageTotal(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.villageTotal(areaName));
    }

    /**
     * 民主法治村默认查询，驾驶舱用
     */
    @GetMapping("/bigScreen/lawPosition/getDefault")
    @ApiOperation("民主法治村默认查询，驾驶舱用")
    public ResponseData getDefault(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(lawPositionService.getDefault(areaName));
    }

    /**
     * 根据名称搜索民主法治村
     */
    @ApiOperation("根据名称搜索民主法治村")
    @GetMapping("/bigScreen/villageSearch")
    public ResponseData villageSearch(@RequestParam String name) {
        return new SuccessResponseData(newBigScreenService.villageSearch(name));
    }

    /**
     * 法治文化基地列表
     */
    @ApiOperation("法治文化基地列表")
    @GetMapping("/bigScreen/cultureList")
    public ResponseData cultureList(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.cultureList(areaName));
    }

    /**
     * 法治文化基地数量统计
     */
    @ApiOperation("法治文化基地数量统计")
    @GetMapping("/bigScreen/cultureTotal")
    public ResponseData cultureTotal(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false) Integer type) {
        return new SuccessResponseData(newBigScreenService.cultureTotal(areaName, type));
    }

    /**
     * 法治文化基地 按照区域统计下级数量和提供列表
     */
    @ApiOperation("法治文化基地 按照区域统计下级数量和提供列表")
    @GetMapping("/bigScreen/cultureDetail")
    public ResponseData cultureDetail(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false) Integer type) {
        return new SuccessResponseData(newBigScreenService.cultureDetail(areaName, type));
    }

    /**
     * 法治文化基地 按照区域和名称和类型来搜索
     */
    @ApiOperation("法治文化基地 按照区域和名称和类型来搜索")
    @GetMapping("/bigScreen/cultureSearch")
    public ResponseData cultureSearch(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false) String name, @RequestParam(required = false) Integer type, @RequestParam(required = false) Integer positionLevel) {
        return new SuccessResponseData(newBigScreenService.cultureSearch(areaName, name, type, positionLevel));
    }

    /**
     * 普法信息报送统计
     */
    @ApiOperation("普法信息报送统计")
    @GetMapping("/bigScreen/lawReport")
    public ResponseData lawReport(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawReport(areaName));
    }

    /**
     * 普法信息报送统计-分页

     */
    @ApiOperation("普法信息报送统计-分页")
    @GetMapping("/bigScreen/lawReportPage")
    public ResponseData lawReportPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawReportPage(areaName));
    }

    /**
     * 普法宣传-浙里普法访问人次
     */
    @ApiOperation("普法宣传-浙里普法访问人次")
    @GetMapping("/bigScreen/lawVisit")
    public ResponseData lawVisit(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName,
                                @RequestParam(required = false) Integer year) {
        return new SuccessResponseData(newBigScreenService.lawVisit(areaName, year));
    }

    /**
     * 普法宣传-群众学法
     */
    @ApiOperation("普法宣传-群众学法")
    @GetMapping("/bigScreen/lawLearn")
    public ResponseData lawLearn(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawLearn(areaName));
    }

    /**
     * 普法宣传-群众学法-分页
     */
    @ApiOperation("普法宣传-群众学法-分页")
    @GetMapping("/bigScreen/lawLearnPage")
    public ResponseData lawLearnPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawLearnPage(areaName));
    }

    /**
     * 普法宣传-普法评测
     */
    @ApiOperation("普法宣传-普法评测")
    @GetMapping("/bigScreen/lawTest")
    public ResponseData lawTest(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawTest(areaName));
    }

    /**
     * 普法宣传-普法评测-分页
     */
    @ApiOperation("普法宣传-普法评测-分页")
    @GetMapping("/bigScreen/lawTestPage")
    public ResponseData lawTestPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false) Integer type) {
        return new SuccessResponseData(newBigScreenService.lawTestPage(areaName, type));
    }

    /**
     * 普法宣传-浙里普法资讯统计
     */
    @ApiOperation("普法宣传-浙里普法资讯统计")
    @GetMapping("/bigScreen/lawNews")
    public ResponseData lawNews(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawNews(areaName));
    }

    /**
     * 普法宣传-浙里普法资讯统计-分页
     */
    @ApiOperation("普法宣传-浙里普法资讯统计-分页")
    @GetMapping("/bigScreen/lawNewsPage")
    public ResponseData lawNewsPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName,
     @RequestParam(required = false) String labelName) {
        return new SuccessResponseData(newBigScreenService.lawNewsPage(areaName, labelName));
    }

    /**
     * 普法宣传-普法热度洞察
     */
    @ApiOperation("普法宣传-普法热度洞察")
    @GetMapping("/bigScreen/lawHot")
    public ResponseData lawHot(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawHot(areaName));
    }

    /**
     * 普法宣传-按照区域查询信息报送统计
     */
    @ApiOperation("普法宣传-按照区域查询信息报送统计-地图中央区域")
    @GetMapping("/bigScreen/lawReportByArea")
    public ResponseData lawReportByArea(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawReportByArea(areaName));
    }

    /**
     * 法治文化-法治文化阵地
     */
    @ApiOperation("法治文化-法治文化阵地")
    @GetMapping("/bigScreen/lawCulture")
    public ResponseData lawCulture(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawCulture(areaName));
    }

    /**
     * 法治文化-法治文化阵地-分页
     */
    @ApiOperation("法治文化-法治文化阵地-分页")
    @GetMapping("/bigScreen/lawCulturePage")
    public ResponseData lawCulturePage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false) Integer type, @RequestParam(required = false) Integer positionLevel) {
        return new SuccessResponseData(newBigScreenService.lawCulturePage(areaName, type, positionLevel));
    }

    /**
     * 法治文化-法治文化阵地-地图点位展示
     */
    @ApiOperation("法治文化-法治文化阵地-地图点位展示")
    @GetMapping("/bigScreen/lawCultureMap")
    public ResponseData lawCultureMap(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false) Integer type, @RequestParam(required = false) Integer positionLevel) {
        return new SuccessResponseData(newBigScreenService.lawCultureMap(areaName, type, positionLevel));
    }


    /**
     * 法治文化-各地法治文化阵地
     */
    @ApiOperation("法治文化-各地法治文化阵地")
    @GetMapping("/bigScreen/lawCultureByArea")
    public ResponseData lawCultureByArea(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawCultureByArea(areaName));
    }

    /**
     * 法治文化-各地法治文化阵地列表-分页
     */
    @ApiOperation("法治文化-各地法治文化阵地列表-分页")
    @GetMapping("/bigScreen/lawCultureList")
    public ResponseData lawCultureList(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName,
                                       @RequestParam(required = false) Integer level) {
        return new SuccessResponseData(newBigScreenService.lawCultureListPage(areaName, level));
    }

    /**
     * 法治文化-各地市普法活动总量统计
     */
    @ApiOperation("法治文化-各地市普法活动总量统计")
    @GetMapping("/bigScreen/lawActivityTotal")
    public ResponseData lawActivityTotal(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawActivityTotal(areaName));
    }

    /**
     * 法治文化-法治文艺库
     */
    @ApiOperation("法治文化-法治文艺库")
    @GetMapping("/bigScreen/lawArt")
    public ResponseData lawArt(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawArt(areaName));
    }

    /**
     * 法治文化-法治文艺库-分页
     */
    @ApiOperation("法治文化-法治文艺库-分页")
    @GetMapping("/bigScreen/lawArtPage")
    public ResponseData lawArtPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false) String type) {
        Integer typeInt = null;
        if (type != null) {
            if ("total".equals(type)) {
                // 处理特殊值 "total"，可以根据业务需求设置对应的整数值
                typeInt = -1; // 假设 -1 表示 "total"，根据实际业务逻辑调整
            } else {
                try {
                    typeInt = Integer.parseInt(type);
                } catch (NumberFormatException e) {
                    // 转换失败时使用默认值或返回错误
                    typeInt = null;
                }
            }
        }
        return new SuccessResponseData(newBigScreenService.lawArtPage(areaName, typeInt));
    }

    /**
     * 法治文化-文艺库主题分类
     */
    @ApiOperation("法治文化-文艺库主题分类")
    @GetMapping("/bigScreen/lawArtCategory")
    public ResponseData lawArtCategory(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawArtCategory(areaName));
    }

    /**
     * 法治文化-文艺库主题分类-分页
     */
    @ApiOperation("法治文化-文艺库主题分类-分页")
    @GetMapping("/bigScreen/lawArtCategoryPage")
    public ResponseData lawArtCategoryPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false) String labelName) {
        return new SuccessResponseData(newBigScreenService.lawArtCategoryPage(areaName, labelName));
    }

    /**
     * 法治文化-文艺库面相群体分类
     */
    @ApiOperation("法治文化-文艺库面相群体分类")
    @GetMapping("/bigScreen/lawArtGroup")
    public ResponseData lawArtGroup(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawArtGroup(areaName));
    }


    /**
     * 随机给资源绑定标签
     */
    @ApiOperation("随机给资源绑定标签-测试用")
    @GetMapping("/bigScreen/lawResourceTag")
    public ResponseData lawResourceTag() {
        newBigScreenService.lawResourceTag();
        return new SuccessResponseData();
    }

    /**
     * 依法治理-普法队伍统计分析
     */
    @ApiOperation("依法治理-普法队伍统计分析")
    @GetMapping("/bigScreen/lawCultivateAnalyse")
    public ResponseData lawCultivateAnalyse(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawCultivateAnalyse(areaName));
    }

    /**
     * 依法治理-普法队伍统计分析-各地市区县分布
     *
     * @param type 1-普法干部 2-法律明白人 3-普法志愿者
     */
    @ApiOperation("依法治理-普法队伍统计分析-各地市区县分布")
    @GetMapping("/bigScreen/lawCultivateAnalyseArea")
    public ResponseData lawCultivateAnalyseArea(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName,
                                                @RequestParam(required = false, defaultValue = "1") Integer type) {
        return new SuccessResponseData(newBigScreenService.lawCultivateAnalyseArea(areaName, type));
    }

    /**
     * 依法治理-各地民主法治示范村
     */
    @ApiOperation("依法治理-各地民主法治示范村")
    @GetMapping("/bigScreen/lawVillage")
    public ResponseData lawVillage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawVillage(areaName));
    }

    /**
     * 依法治理-各地民主法治示范村-总数统计和下级行政单位数量统计
     */
    @ApiOperation("依法治理-各地民主法治示范村-总数统计和下级行政单位数量统计（地图用））")
    @GetMapping("/bigScreen/lawVillageTotal")
    public ResponseData lawVillageTotal(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawVillageTotal(areaName));
    }

    /**
     * 依法治理-公民法治素养观测点
     */
    @ApiOperation("依法治理-公民法治素养观测点")
    @GetMapping("/bigScreen/lawObservatory")
    public ResponseData lawObservatory(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawObservatory(areaName));
    }

    /**
     * 依法治理-普法队伍统计分析-详情分页
     */
    @ApiOperation("依法治理-普法队伍统计分析-详情分页")
    @GetMapping("/bigScreen/lawCultivateAnalysePage")
    public ResponseData lawCultivateAnalysePage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName,
                                                @RequestParam(required = false) Integer type) {
        return new SuccessResponseData(newBigScreenService.lawCultivateAnalysePage(areaName, type));
    }


    /**
     * 依法治理-普法队伍统计分析-各地市区县分布-人员详情分页
     */
    @ApiOperation("依法治理-普法队伍统计分析-各地市区县分布-人员详情分页")
    @GetMapping("/bigScreen/lawCultivateAnalyseAreaUserPage")
    public ResponseData lawCultivateAnalyseAreaUserPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName,
                                                        @RequestParam(required = false, defaultValue = "1") Integer type) {
        return new SuccessResponseData(newBigScreenService.lawCultivateAnalyseAreaUserPage(areaName, type));
    }

    /**
     * 依法治理-各地民主法治示范村-详情分页
     */
    @ApiOperation("依法治理-各地民主法治示范村-详情分页")
    @GetMapping("/bigScreen/lawVillagePage")
    public ResponseData lawVillagePage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawVillagePage(areaName));
    }

    /**
     * 依法治理-各地民主法治示范村-总数统计分页
     */
    @ApiOperation("依法治理-各地民主法治示范村-总数统计分页")
    @GetMapping("/bigScreen/lawVillageTotalPage")
    public ResponseData lawVillageTotalPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName,
                                            @RequestParam(required = false) Integer level) {
        return new SuccessResponseData(newBigScreenService.lawVillageTotalPage(areaName, level));
    }

    /**
     * 依法治理-各地民主法治示范村-下级行政单位数量统计-详情分页
     */
    @ApiOperation("依法治理-各地民主法治示范村-下级行政单位数量统计-详情分页")
    @GetMapping("/bigScreen/lawVillageAreaPage")
    public ResponseData lawVillageAreaPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawVillageTotalPage(areaName, null));
    }

    /**
     * 依法治理-公民法治素养基准分页
     */
    @ApiOperation("依法治理-公民法治素养基准分页")
    @GetMapping("/bigScreen/citizenLegalLiteracyPage")
    public ResponseData lawCultivatePage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName, @RequestParam(required = false, defaultValue = "1") Integer type) {
        return new SuccessResponseData(newBigScreenService.citizenLegalLiteracyPage(areaName, type));
    }

    /**
     * 依法治理-公民法治素养观测点-详情分页
     */
    @ApiOperation("依法治理-公民法治素养观测点-详情分页")
    @GetMapping("/bigScreen/lawObservatoryPage")
    public ResponseData lawObservatoryPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName,
                                           @RequestParam(required = false) Integer level) {
        return new SuccessResponseData(newBigScreenService.lawObservatoryPage(areaName, level));
    }

    /**
     * 依法治理-民主法治示范村(社区)特色
     */
    @ApiOperation("依法治理-民主法治示范村(社区)特色")
    @GetMapping("/bigScreen/lawVillageFeature")
    public ResponseData lawVillageFeature(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawVillageFeature(areaName));
    }

    /**
     * 法治文化-主题宣传月
     *
     */
    @ApiOperation("法治文化-主题宣传月")
    @GetMapping("/bigScreen/lawThemeMonth")
    public ResponseData lawThemeMonth(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawThemeMonth(areaName));
    }

    /**
     * 法治文化-主题宣传月-分页
     */
    @ApiOperation("法治文化-主题宣传月-分页")
    @GetMapping("/bigScreen/lawThemeMonthPage")
    public ResponseData lawThemeMonthPage(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawThemeMonthPage(areaName));
    }

    /**
     * 法治文化-主题宣传月-视频轮播
     */
    @ApiOperation("法治文化-主题宣传月-视频轮播")
    @GetMapping("/bigScreen/lawThemeMonthVideo")
    public ResponseData lawThemeMonthVideo(@RequestParam(required = false, defaultValue = CommonConstant.TOP_AREA_NAME) String areaName) {
        return new SuccessResponseData(newBigScreenService.lawThemeMonthVideo(areaName));
    }

    /**
     * 随机造主题宣传月活动数据
     */
    @ApiOperation("随机造主题宣传月活动数据-测试用")
    @GetMapping("/bigScreen/lawThemeMonthData")
    public ResponseData lawThemeMonthData() {
        newBigScreenService.lawThemeMonthData();
        return new SuccessResponseData();
    }

    /**
     * 普法活动主题
     */
    @ApiOperation("普法活动主题")
    @GetMapping("/bigScreen/lawActivityTheme")
    public ResponseData lawActivityTheme() {
        return new SuccessResponseData(newBigScreenService.lawActivityTheme());
    }

    /**
     * 普法活动趋势
     */
    @ApiOperation("普法活动趋势")
    @GetMapping("/bigScreen/lawActivityTrend")
    public ResponseData lawActivityTrend() {
        return new SuccessResponseData(newBigScreenService.lawActivityTrend());
    }

    /**
     * 普法活动风采
     */
    @ApiOperation("普法活动风采")
    @GetMapping("/bigScreen/lawActivityStyle")
    public ResponseData lawActivityStyle() {
        return new SuccessResponseData(newBigScreenService.lawActivityStyle());
    }

    /**
     * 普法活动列表
     */
    @ApiOperation("普法活动列表")
    @GetMapping("/bigScreen/lawActivityList")
    public ResponseData lawActivityList() {
        return new SuccessResponseData(newBigScreenService.lawActivityList());
    }

}
