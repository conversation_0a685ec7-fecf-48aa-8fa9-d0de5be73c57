package com.concise.modular.statistic.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.concise.common.file.util.OssBootUtil;
import com.concise.common.file.util.OssSignedUrlUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.common.consts.CommonConstant;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.benchmark.benchmarkmanage.entity.BenchmarkManage;
import com.concise.modular.benchmark.benchmarkmanage.service.BenchmarkManageService;
import com.concise.modular.evaluation.cityareainfo.mapper.CityAreaInfoMapper;
import com.concise.modular.evaluation.cityareainfo.service.CityAreaInfoService;
import com.concise.modular.law.lawposition.entity.LawPosition;
import com.concise.modular.law.lawposition.enums.LawPositionBaseEnum;
import com.concise.modular.law.lawposition.service.LawPositionService;
import com.concise.modular.laweducation.entity.LeResources;
import com.concise.modular.laweducation.entity.LerLabel;
import com.concise.modular.laweducation.mapper.LerLabelMapper;
import com.concise.modular.laweducation.param.LerLabelParam;
import com.concise.modular.laweducation.service.LawEducationResourcesViewService;
import com.concise.modular.laweducation.service.LeResourcesService;
import com.concise.modular.laweducation.service.LerLabelService;
import com.concise.modular.obpoint.entity.ObPointInfo;
import com.concise.modular.obpoint.service.ObPointInfoService;
import com.concise.modular.statistic.mapper.BigScreenMapper;
import com.concise.modular.statistic.service.BigScreenService;
import com.concise.modular.statistic.service.NewBigScreenService;
import com.concise.modular.statistic.vo.LawUserVo;
import com.concise.sys.modular.dict.entity.SysDictData;
import com.concise.sys.modular.dict.entity.SysDictType;
import com.concise.sys.modular.dict.service.SysDictDataService;
import com.concise.sys.modular.dict.service.SysDictTypeService;
import com.concise.sys.modular.log.entity.SysVisLog;
import com.concise.sys.modular.log.service.SysVisLogService;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 新版大屏服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
@Service
public class NewBigScreenServiceImpl implements NewBigScreenService {

    @Resource
    private BigScreenService bigScreenService;

    @Resource
    private BigScreenMapper bigScreenMapper;
    @Resource
    private LeResourcesService leResourcesService;

    @Resource
    private LawPositionService lawPositionService;

    @Resource
    private ObPointInfoService obPointInfoService;

    @Resource
    private SysOrgService sysOrgService;

    @Resource
    private BenchmarkManageService benchmarkManageService;

    @Resource
    private CityAreaInfoMapper cityAreaInfoMapper;

    @Resource
    private LerLabelService lerLabelService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private LerLabelMapper lerLabelMapper;

    @Resource
    private LawEducationResourcesViewService lawEducationResourcesViewService;

    @Resource
    private SysVisLogService sysVisLogService;

    @Resource
    private SysDictDataService sysDictDataService;

    @Resource
    private SysDictTypeService sysDictTypeService;

    @Resource
    private CityAreaInfoService cityAreaInfoService;

    @Resource
    private SysFileInfoService sysFileInfoService;

    @Override
    public Map<String, Object> villageList(String areaName) {
        // 临时使用委托方式获取区域编码
        String areaCode = null;
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaCode = CommonConstant.TOP_AREA_CODE;
        } else {
            // 暂时委托给原service获取areaCode
            Map<String, Object> tempResult = bigScreenService.villageList(areaName);
            return tempResult; // 临时返回，后续完善
        }

        List<LawPosition> list = bigScreenMapper.villageList(areaCode);
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(list)) {
            map.put("list", list);
            map.put("total", list.size());
            return map;
        }
        map.put("list", new ArrayList<>());
        map.put("total", 0);
        return map;
    }

    @Override
    public Map<String, Object> villageTotal(String areaName) {
        Map<String, Object> map = new HashMap<>();
        //如果是省级，返回总数和各地市数量
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            map.put("total", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4)));
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                jsonObject.put("value", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4).eq(LawPosition::getCity, city)));
                jsonObjectList.add(jsonObject);
            }
            map.put("cityList", jsonObjectList);
            //返回全国法治文化教育基地
            List<LawPosition> list = lawPositionService.list(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4).eq(LawPosition::getPositionLevel, 1));
            if (CollectionUtil.isNotEmpty(list)) {
                //遍历查询出图片和视频
                for (LawPosition lawPosition : list) {
                    if (ObjectUtil.isNotEmpty(lawPosition.getPicId())) {
                        List<SysFileInfo> picList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getPicId().split(SymbolConstant.COMMA))));
                        lawPosition.setPicList(picList);
                    }
                    if (ObjectUtil.isNotEmpty(lawPosition.getVideoId())) {
                        List<SysFileInfo> videoList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getVideoId().split(SymbolConstant.COMMA))));
                        lawPosition.setVideoList(videoList);
                    }
                }
            }
            map.put("provinceList", list);
            return map;
        }

        String areaCode = getAreaCodeByName(areaName);
        if (ObjectUtil.isNotEmpty(areaCode)) {
            map.put("total", bigScreenMapper.villageTotal(areaCode));
            map.put("cityList", bigScreenMapper.villageTotalCity(areaCode));
            List<LawPosition> list = lawPositionService.list(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4).like(LawPosition::getAddressCode, areaCode));
            if (CollectionUtil.isNotEmpty(list)) {
                //遍历查询出图片和视频
                for (LawPosition lawPosition : list) {
                    if (ObjectUtil.isNotEmpty(lawPosition.getPicId())) {
                        List<SysFileInfo> picList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getPicId().split(SymbolConstant.COMMA))));
                        lawPosition.setPicList(picList);
                    }
                    if (ObjectUtil.isNotEmpty(lawPosition.getVideoId())) {
                        List<SysFileInfo> videoList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getVideoId().split(SymbolConstant.COMMA))));
                        lawPosition.setVideoList(videoList);
                    }
                }
                map.put("positionList", list);
            } else {
                map.put("positionList", new ArrayList<>());
            }
            return map;
        }
        map.put("total", 0);
        map.put("cityList", new ArrayList<>());
        return map;
    }

    @Override
    public List<LawPosition> villageSearch(String name) {
        return lawPositionService.list(new QueryWrapper<LawPosition>().lambda()
                .and(e -> e.like(LawPosition::getPositionName, name)
                        .or().like(LawPosition::getCity, name)
                        .or().like(LawPosition::getArea, name))
                .eq(LawPosition::getPositionType, 4));
    }

    @Override
    public List<JSONObject> cultureList(String areaName) {
        List<LawPosition> list = lawPositionService.list(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12).like(LawPosition::getAddressCode, getAreaCodeByName(areaName)));
        List<JSONObject> jsonObjectList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            //遍历枚举，配置数量和列表
            for (LawPositionBaseEnum lawPositionTypeEnum : LawPositionBaseEnum.values()) {
                int count = 0;
                List<LawPosition> lawPositionList = list.stream().filter(e -> Objects.equals(e.getPositionType(), lawPositionTypeEnum.getCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(lawPositionList)) {
                    count = lawPositionList.size();
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", lawPositionTypeEnum.getDescription());
                jsonObject.put("value", count);
                jsonObject.put("list", lawPositionList);
                jsonObjectList.add(jsonObject);
            }
        } else {
            for (LawPositionBaseEnum lawPositionTypeEnum : LawPositionBaseEnum.values()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", lawPositionTypeEnum.getDescription());
                jsonObject.put("value", 0);
                jsonObject.put("list", new ArrayList<>());
                jsonObjectList.add(jsonObject);
            }
        }
        return jsonObjectList;
    }

    @Override
    public Map<String, Object> cultureTotal(String areaName, Integer type) {
        Map<String, Object> map = new HashMap<>();
        String areaCodeByName = getAreaCodeByName(areaName);
        if (ObjectUtil.isNotEmpty(areaCodeByName)) {
            // 准备查询条件和结果容器
            Map<String, Object> provinceMap = new HashMap<>();
            Map<String, Object> cityMap = new HashMap<>();
            map.put("province", provinceMap);
            map.put("provinceName", "全国法治文化教育基地");
            map.put("city", cityMap);
            map.put("cityName", "省级法治文化教育基地");

            // 省级和市级的总数查询条件
            QueryWrapper<LawPosition> provinceQuery = new QueryWrapper<>();
            provinceQuery.lambda().eq(LawPosition::getPositionLevel, 1)
                    .in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);

            QueryWrapper<LawPosition> cityQuery = new QueryWrapper<>();
            cityQuery.lambda().eq(LawPosition::getPositionLevel, 2)
                    .in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);

            if (ObjectUtil.isNotEmpty(areaCodeByName)) {
                provinceQuery.lambda().like(LawPosition::getAddressCode, areaCodeByName);
                cityQuery.lambda().like(LawPosition::getAddressCode, areaCodeByName);
            }
            if (type != null) {
                provinceQuery.lambda().eq(LawPosition::getPositionType, type);
                cityQuery.lambda().eq(LawPosition::getPositionType, type);
            }

            // 执行总数查询
            provinceMap.put("provinceTotal", lawPositionService.count(provinceQuery));
            //列表详情
            List<LawPosition> list = lawPositionService.list(provinceQuery);
            if (CollectionUtil.isNotEmpty(list)) {
                //遍历查询出图片和视频
                for (LawPosition lawPosition : list) {
                    if (ObjectUtil.isNotEmpty(lawPosition.getPicId())) {
                        List<SysFileInfo> picList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getPicId().split(SymbolConstant.COMMA))));
                        lawPosition.setPicList(picList);
                    }
                    if (ObjectUtil.isNotEmpty(lawPosition.getVideoId())) {
                        List<SysFileInfo> videoList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getVideoId().split(SymbolConstant.COMMA))));
                        lawPosition.setVideoList(videoList);
                    }
                }
            }
            provinceMap.put("pointList", list);
            cityMap.put("cityTotal", lawPositionService.count(cityQuery));

            // 批量查询各类型数量
            // 1. 构建查询条件 - 查询所有符合条件的数据
            QueryWrapper<LawPosition> allTypesQuery = new QueryWrapper<>();
            allTypesQuery.lambda()
                    .in(LawPosition::getPositionLevel, 1, 2)
                    .in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);

            if (ObjectUtil.isNotEmpty(areaCodeByName)) {
                allTypesQuery.lambda().like(LawPosition::getAddressCode, areaCodeByName);
            }

            // 2. 执行一次查询获取所有数据
            List<LawPosition> allPositions = lawPositionService.list(allTypesQuery);

            // 3. 在内存中处理数据分类统计
            Map<String, Integer> provinceTypeCountMap = new HashMap<>();
            Map<String, Integer> cityTypeCountMap = new HashMap<>();

            // 初始化计数器，确保结果中包含所有类型
            for (LawPositionBaseEnum typeEnum : LawPositionBaseEnum.values()) {
                provinceTypeCountMap.put(typeEnum.getCode(), 0);
                cityTypeCountMap.put(typeEnum.getCode(), 0);
            }

            // 根据查询结果更新计数
            for (LawPosition position : allPositions) {
                if ("1".equals(position.getPositionLevel())) {
                    // 省级数据
                    String typeCode = position.getPositionType();
                    provinceTypeCountMap.put(typeCode, provinceTypeCountMap.getOrDefault(typeCode, 0) + 1);
                } else if ("2".equals(position.getPositionLevel())) {
                    // 市级数据
                    String typeCode = position.getPositionType();
                    cityTypeCountMap.put(typeCode, cityTypeCountMap.getOrDefault(typeCode, 0) + 1);
                }
            }

            // 4. 构建结果
            List<JSONObject> provinceList = new ArrayList<>();
            List<JSONObject> cityList = new ArrayList<>();

            for (LawPositionBaseEnum lawPositionTypeEnum : LawPositionBaseEnum.values()) {
                String code = lawPositionTypeEnum.getCode();

                // 省级数据
                JSONObject provinceTypeObj = new JSONObject();
                provinceTypeObj.put("name", lawPositionTypeEnum.getDescription());
                provinceTypeObj.put("code", code);
                provinceTypeObj.put("value", provinceTypeCountMap.get(code));
                provinceList.add(provinceTypeObj);

                // 市级数据
                JSONObject cityTypeObj = new JSONObject();
                cityTypeObj.put("name", lawPositionTypeEnum.getDescription());
                cityTypeObj.put("code", code);
                cityTypeObj.put("value", cityTypeCountMap.get(code));
                cityList.add(cityTypeObj);
            }

            provinceMap.put("provinceList", provinceList);
            cityMap.put("cityList", cityList);

            //根据类型查询各地市数量分布，兼容type为空的情况
            List<JSONObject> cityPointList = new ArrayList<>();

            // 判断当前区域层级，实现向下钻取
            if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
                // 省级：显示各地市数据
                for (String city : CommonConstant.CITY_LIST) {
                    String areaCode = getAreaCodeByName(city);
                    QueryWrapper<LawPosition> cityQueryWrapper = new QueryWrapper<>();

                    // 根据type参数决定是否添加类型过滤条件
                    if (type != null) {
                        cityQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
                    }

                    cityQueryWrapper.lambda().like(LawPosition::getAddressCode, areaCode);
                    JSONObject cityPoint = new JSONObject();
                    cityPoint.put("name", city);
                    cityPoint.put("total", lawPositionService.count(cityQueryWrapper));
                    cityPointList.add(cityPoint);
                }
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                // 地市级：显示各区县数据
                List<String> childAreaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
                for (String area : childAreaNameList) {
                    String areaCode = getAreaCodeByName(area);
                    QueryWrapper<LawPosition> areaQueryWrapper = new QueryWrapper<>();

                    // 根据type参数决定是否添加类型过滤条件
                    if (type != null) {
                        areaQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
                    }

                    areaQueryWrapper.lambda().like(LawPosition::getAddressCode, areaCode);
                    JSONObject areaPoint = new JSONObject();
                    areaPoint.put("name", area);
                    areaPoint.put("total", lawPositionService.count(areaQueryWrapper));
                    cityPointList.add(areaPoint);
                }
            } else {
                // 区县级：不再向下钻取，返回空列表或当前区县的详细信息
                // 可以根据需要返回当前区县的具体数据
            }

            map.put("cityPointList", cityPointList);

            return map;
        }

        return Collections.emptyMap();
    }

    @Override
    public Map<String, Object> cultureDetail(String areaName, Integer type) {
        Map<String, Object> map = new HashMap<>();

        //省级查看地市的数量
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 国家级数量列表
            List<JSONObject> nationalAmountList = new ArrayList<>();
            // 省级数量列表
            List<JSONObject> provinceAmountList = new ArrayList<>();

            CommonConstant.CITY_LIST.forEach(city -> {
                String areaCodeByName = getAreaCodeByName(city);

                // 查询国家级数量
                QueryWrapper<LawPosition> nationalQueryWrapper = new QueryWrapper<>();
                if (type != null) {
                    nationalQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
                } else {
                    nationalQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
                }
                nationalQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 1).like(LawPosition::getAddressCode, areaCodeByName);
                JSONObject nationalObject = new JSONObject();
                nationalObject.put("name", city);
                nationalObject.put("total", lawPositionService.count(nationalQueryWrapper));
                nationalAmountList.add(nationalObject);

                // 查询省级数量
                QueryWrapper<LawPosition> provinceQueryWrapper = new QueryWrapper<>();
                if (type != null) {
                    provinceQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
                } else {
                    provinceQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
                }
                provinceQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 2).like(LawPosition::getAddressCode, areaCodeByName);
                JSONObject provinceObject = new JSONObject();
                provinceObject.put("name", city);
                provinceObject.put("total", lawPositionService.count(provinceQueryWrapper));
                provinceAmountList.add(provinceObject);
            });

            map.put("nationalAmountList", nationalAmountList);
            map.put("provinceAmountList", provinceAmountList);

            //省级也要返回全国所有点位列表
            String topAreaCode = getAreaCodeByName(areaName);

            // 全国国家级点位列表
            QueryWrapper<LawPosition> nationalAllQueryWrapper = new QueryWrapper<>();
            if (type != null) {
                nationalAllQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
            } else {
                nationalAllQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
            }
            nationalAllQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 1).like(LawPosition::getAddressCode, topAreaCode);
            List<LawPosition> nationalAllLawPositions = lawPositionService.list(nationalAllQueryWrapper);
            List<JSONObject> nationalPositionList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(nationalAllLawPositions)) {
                for (LawPosition lawPosition : nationalAllLawPositions) {
                    // 添加图片和视频信息
                    lawPosition = addMediaInfo(lawPosition);
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(lawPosition);
                    nationalPositionList.add(jsonObject);
                }
            }
            map.put("nationalPositionList", nationalPositionList);
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            //地市级查看区县的数量
            // 国家级区县数量列表
            List<JSONObject> nationalAmountList = new ArrayList<>();
            // 省级区县数量列表
            List<JSONObject> provinceAmountList = new ArrayList<>();

            List<String> childAreaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
            for (String area : childAreaNameList) {
                String areaCodeByName = getAreaCodeByName(area);

                // 查询国家级数量
                QueryWrapper<LawPosition> nationalQueryWrapper = new QueryWrapper<>();
                if (type != null) {
                    nationalQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
                } else {
                    nationalQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
                }
                nationalQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 1).like(LawPosition::getAddressCode, areaCodeByName);
                JSONObject nationalObject = new JSONObject();
                nationalObject.put("name", area);
                nationalObject.put("total", lawPositionService.count(nationalQueryWrapper));
                nationalAmountList.add(nationalObject);

                // 查询省级数量
                QueryWrapper<LawPosition> provinceQueryWrapper = new QueryWrapper<>();
                if (type != null) {
                    provinceQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
                } else {
                    provinceQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
                }
                provinceQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 2).like(LawPosition::getAddressCode, areaCodeByName);
                JSONObject provinceObject = new JSONObject();
                provinceObject.put("name", area);
                provinceObject.put("total", lawPositionService.count(provinceQueryWrapper));
                provinceAmountList.add(provinceObject);
            }

            map.put("nationalAmountList", nationalAmountList);
            map.put("provinceAmountList", provinceAmountList);

            //地市级也要返回所有点位列表
            String cityAreaCode = getAreaCodeByName(areaName);

            // 国家级点位列表
            QueryWrapper<LawPosition> nationalCityQueryWrapper = new QueryWrapper<>();
            if (type != null) {
                nationalCityQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
            } else {
                nationalCityQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
            }
            nationalCityQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 1).like(LawPosition::getAddressCode, cityAreaCode);
            List<LawPosition> nationalCityLawPositions = lawPositionService.list(nationalCityQueryWrapper);
            List<JSONObject> nationalPositionList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(nationalCityLawPositions)) {
                for (LawPosition lawPosition : nationalCityLawPositions) {
                    // 添加图片和视频信息
                    lawPosition = addMediaInfo(lawPosition);
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(lawPosition);
                    nationalPositionList.add(jsonObject);
                }
            }
            map.put("nationalPositionList", nationalPositionList);

            // 省级点位列表
            QueryWrapper<LawPosition> provinceCityQueryWrapper = new QueryWrapper<>();
            if (type != null) {
                provinceCityQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
            } else {
                provinceCityQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
            }
            provinceCityQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 2).like(LawPosition::getAddressCode, cityAreaCode);
            List<LawPosition> provinceCityLawPositions = lawPositionService.list(provinceCityQueryWrapper);
            List<JSONObject> provincePositionList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(provinceCityLawPositions)) {
                for (LawPosition lawPosition : provinceCityLawPositions) {
                    // 添加图片和视频信息
                    lawPosition = addMediaInfo(lawPosition);
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(lawPosition);
                    provincePositionList.add(jsonObject);
                }
            }
            map.put("provincePositionList", provincePositionList);
        } else {
            //区县直接查列表
            String areaCode = getAreaCodeByName(areaName);

            // 国家级点位列表
            QueryWrapper<LawPosition> nationalQueryWrapper = new QueryWrapper<>();
            if (type != null) {
                nationalQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
            } else {
                nationalQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
            }
            nationalQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 1).like(LawPosition::getAddressCode, areaCode);
            List<LawPosition> nationalLawPositions = lawPositionService.list(nationalQueryWrapper);
            List<JSONObject> nationalPositionList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(nationalLawPositions)) {
                for (LawPosition lawPosition : nationalLawPositions) {
                    // 添加图片和视频信息
                    lawPosition = addMediaInfo(lawPosition);
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(lawPosition);
                    nationalPositionList.add(jsonObject);
                }
            }
            map.put("nationalPositionList", nationalPositionList);

            // 省级点位列表
            QueryWrapper<LawPosition> provinceQueryWrapper = new QueryWrapper<>();
            if (type != null) {
                provinceQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
            } else {
                provinceQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
            }
            provinceQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 2).like(LawPosition::getAddressCode, areaCode);
            List<LawPosition> provinceLawPositions = lawPositionService.list(provinceQueryWrapper);
            List<JSONObject> provincePositionList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(provinceLawPositions)) {
                for (LawPosition lawPosition : provinceLawPositions) {
                    // 添加图片和视频信息
                    lawPosition = addMediaInfo(lawPosition);
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(lawPosition);
                    provincePositionList.add(jsonObject);
                }
            }
            map.put("provincePositionList", provincePositionList);

            //区县级不需要返回数量列表，因为已经是最底层了
            map.put("nationalAmountList", new ArrayList<>());
            map.put("provinceAmountList", new ArrayList<>());
        }
        return map;
    }

    @Override
    public List<JSONObject> cultureSearch(String areaName, String name, Integer type, Integer positionLevel) {
        String areaCodeByName = getAreaCodeByName(areaName);
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        if (type != null) {
            queryWrapper.lambda().eq(LawPosition::getPositionType, type);
        } else {
            queryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
        }
        if (positionLevel != null) {
            queryWrapper.lambda().eq(LawPosition::getPositionLevel, positionLevel);
        }
        if (ObjectUtil.isNotEmpty(name)) {
            queryWrapper.lambda().like(LawPosition::getPositionName, name);
        }
        queryWrapper.lambda().like(LawPosition::getAddressCode, areaCodeByName);
        List<LawPosition> lawPositions = lawPositionService.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(lawPositions)) {
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (LawPosition lawPosition : lawPositions) {
                // 添加图片和视频信息
                lawPosition = addMediaInfo(lawPosition);
                // 先将对象转换为JSONObject
                JSONObject jsonObject = (JSONObject) JSONObject.toJSON(lawPosition);
                jsonObjectList.add(jsonObject);
            }
            return jsonObjectList;
        }
        return Collections.emptyList();
    }

    @Override
    public List<JSONObject> lawReport(String areaName) {
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            return bigScreenMapper.lawReport();
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            return bigScreenMapper.lawReportCity(areaName);
        } else {
            return bigScreenMapper.lawReportQx(areaName);
        }
    }

    @Override
    public PageResult<JSONObject> lawReportPage(String areaName) {
        // 获取基础数据
        PageResult<JSONObject> pageResult = leResourcesService.lawReportPage(areaName);
        List<JSONObject> resultList = new ArrayList<>();


        // 处理每条记录，添加一级标签和二级标签
        for (JSONObject record : pageResult.getRows()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", record.getString("id"));
            jsonObject.put("title", record.getString("title"));  // 资讯标题
            jsonObject.put("deptName", record.getString("dept_name"));  // 单位
            jsonObject.put("createTime", record.getDate("create_time"));  // 提交时间

            // 获取资源关联的标签
            List<String> firstLevelLabels = lerLabelMapper.selectFirstLabelName(record.getString("id"));
            List<String> secondLevelLabels = lerLabelMapper.selectSecondLabelName(record.getString("id"));
            if (CollectionUtil.isNotEmpty(firstLevelLabels)) {
                String join = String.join(SymbolConstant.COMMA, firstLevelLabels);
                jsonObject.put("firstLevelLabels", join);
            } else {
                jsonObject.put("firstLevelLabels", "");
            }
            if (CollectionUtil.isNotEmpty(secondLevelLabels)) {
                String join = String.join(SymbolConstant.COMMA, secondLevelLabels);
                jsonObject.put("secondLevelLabels", join);
            } else {
                jsonObject.put("secondLevelLabels", "");
            }


            resultList.add(jsonObject);
        }

        // 创建新的PageResult
        PageResult<JSONObject> result = new PageResult<>();
        result.setRows(resultList);
        result.setTotalRows(pageResult.getTotalRows());
        result.setPageNo(pageResult.getPageNo());
        result.setPageSize(pageResult.getPageSize());

        return result;
    }

    @Override
    public List<JSONObject> lawVisit(String areaName, Integer year) {
        List<JSONObject> resultList = new ArrayList<>();

        // 获取当前日期并设置日期格式
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        // 获取当前年份和月份
        int currentYear = calendar.get(Calendar.YEAR);
        int currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar月份从0开始

        // 确定查询的年份
        int targetYear = (year != null) ? year : currentYear;

        // 确定要查询的月份数
        int monthsToQuery = 12;
        if (targetYear == currentYear) {
            monthsToQuery = currentMonth;
        }

        // 查询指定年份的访问数据
        for (int month = 1; month <= monthsToQuery; month++) {
            JSONObject data = new JSONObject();

            // 设置月份
            calendar.set(targetYear, month - 1, 1, 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startDate = calendar.getTime();

            // 设置月末
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            Date endDate = calendar.getTime();

            String monthStr = String.format("%d-%02d", targetYear, month);

            // 构建查询条件
            LambdaQueryWrapper<SysVisLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysVisLog::getRemark, "zlb")
                    .between(SysVisLog::getVisTime, startDate, endDate);

            // 添加地区筛选
            if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
                // 获取区域代码
                String areaCode = getAreaCodeByName(areaName);

                if (StringUtils.isNotBlank(areaCode)) {
                    // 查询属于指定地区的用户账号
                    QueryWrapper<SysUser> userQuery = new QueryWrapper<>();

                    // 根据地区类型进行筛选
                    if (CommonConstant.CITY_LIST.contains(areaName)) {
                        // 如果是地市，使用cityCode筛选
                        userQuery.lambda().eq(SysUser::getCityCode, areaCode);
                    } else {
                        // 如果是区县，使用areaCode筛选
                        userQuery.lambda().eq(SysUser::getAreaCode, areaCode);
                    }

                    List<SysUser> users = sysUserService.list(userQuery);

                    if (CollectionUtil.isNotEmpty(users)) {
                        List<String> accounts = users.stream()
                                .map(SysUser::getAccount)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());

                        if (CollectionUtil.isNotEmpty(accounts)) {
                            queryWrapper.in(SysVisLog::getAccount, accounts);
                        } else {
                            // 如果没有找到对应的用户账号，返回空结果
                            queryWrapper.eq(SysVisLog::getId, -1);
                        }
                    } else {
                        // 如果没有找到对应的用户，返回空结果
                        queryWrapper.eq(SysVisLog::getId, -1);
                    }
                }
            }

            int visits = sysVisLogService.count(queryWrapper);

            data.put("month", monthStr);
            data.put("visits", visits);
            resultList.add(data);
        }

        return resultList;
    }

    /**
     * 普法学习统计
     * 修改说明：
     * 1. 数据来源变更：从 LawEducationResourcesView 表统计改为直接从 LeResources 表获取统计数据
     * 2. 区域筛选变更：从基于用户地区代码筛选改为基于资源单位（deptId）筛选
     * 3. 统计字段变更：观看数使用 LeResources.viewNum，收藏数使用 LeResources.starNum
     * 4. 保持返回结构不变：返回的 JSON 结构与原方法完全一致
     *
     * @param areaName 地区名称
     * @return 包含视频和文章统计数据的Map
     */
    @Override
    public Map<String, Object> lawLearn(String areaName) {
        Map<String, Object> resultMap = new HashMap<>();

        // 获取指定地区的单位ID列表（基于资源单位进行区域筛选）
        Set<String> areaDeptIds = new HashSet<>();
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 根据地区名称获取对应的组织机构ID
            String orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                // 获取该组织机构及其下级机构的所有ID
                areaDeptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 统计普法视频的点赞、观看和收藏数据
        JSONObject videoData = new JSONObject();
        videoData.put("name", "普法视频");

        // 查询视频资源（type以3或4开头的是视频资源）
        QueryWrapper<LeResources> videoQueryWrapper = new QueryWrapper<>();
        videoQueryWrapper.lambda().and(q -> q
                .likeRight(LeResources::getType, "3")
                .or()
                .likeRight(LeResources::getType, "4"));
        videoQueryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源

        // 根据地区筛选资源单位
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            if (CollectionUtil.isNotEmpty(areaDeptIds)) {
                videoQueryWrapper.lambda().in(LeResources::getDeptId, areaDeptIds);
            } else {
                // 如果指定了地区但没有找到对应的单位，则返回空结果
                videoData.put("点赞", 0);
                videoData.put("观看", 0);
                videoData.put("收藏", 0);

                // 统计普法文章数据（同样返回空结果）
                JSONObject articleData = new JSONObject();
                articleData.put("name", "普法文章");
                articleData.put("点赞", 0);
                articleData.put("观看", 0);
                articleData.put("收藏", 0);

                List<JSONObject> resourceData = new ArrayList<>();
                resourceData.add(videoData);
                resourceData.add(articleData);
                resultMap.put("resourceData", resourceData);
                resultMap.put("totalLearners", 0);
                return resultMap;
            }
        }

        List<LeResources> videoResources = leResourcesService.list(videoQueryWrapper);

        // 视频点赞数（直接从LeResources表的likesNum字段统计）
        int videoLikes = videoResources.stream()
                .mapToInt(resource -> resource.getLikesNum() != null ? resource.getLikesNum() : 0)
                .sum();
        videoData.put("点赞", videoLikes);

        // 视频观看数（直接从LeResources表的viewNum字段统计）
        int videoViews = videoResources.stream()
                .mapToInt(resource -> resource.getViewNum() != null ? resource.getViewNum() : 0)
                .sum();
        videoData.put("观看", videoViews);

        // 视频收藏数（直接从LeResources表的starNum字段统计）
        int videoStars = videoResources.stream()
                .mapToInt(resource -> resource.getStarNum() != null ? resource.getStarNum() : 0)
                .sum();
        videoData.put("收藏", videoStars);

        // 统计普法文章的点赞、观看和收藏数据
        JSONObject articleData = new JSONObject();
        articleData.put("name", "普法文章");

        // 查询文章资源（type以1或2开头的是文章/图文资源）
        QueryWrapper<LeResources> articleQueryWrapper = new QueryWrapper<>();
        articleQueryWrapper.lambda().and(q -> q
                .likeRight(LeResources::getType, "1")
                .or()
                .likeRight(LeResources::getType, "2"));
        articleQueryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源

        // 根据地区筛选资源单位
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            if (CollectionUtil.isNotEmpty(areaDeptIds)) {
                articleQueryWrapper.lambda().in(LeResources::getDeptId, areaDeptIds);
            } else {
                // 如果指定了地区但没有找到对应的单位，则返回空结果
                articleData.put("点赞", 0);
                articleData.put("观看", 0);
                articleData.put("收藏", 0);

                List<JSONObject> resourceData = new ArrayList<>();
                resourceData.add(videoData);
                resourceData.add(articleData);
                resultMap.put("resourceData", resourceData);
                resultMap.put("totalLearners", 0);
                return resultMap;
            }
        }

        List<LeResources> articleResources = leResourcesService.list(articleQueryWrapper);

        // 文章点赞数（直接从LeResources表的likesNum字段统计）
        int articleLikes = articleResources.stream()
                .mapToInt(resource -> resource.getLikesNum() != null ? resource.getLikesNum() : 0)
                .sum();
        articleData.put("点赞", articleLikes);

        // 文章观看数（直接从LeResources表的viewNum字段统计）
        int articleViews = articleResources.stream()
                .mapToInt(resource -> resource.getViewNum() != null ? resource.getViewNum() : 0)
                .sum();
        articleData.put("观看", articleViews);

        // 文章收藏数（直接从LeResources表的starNum字段统计）
        int articleStars = articleResources.stream()
                .mapToInt(resource -> resource.getStarNum() != null ? resource.getStarNum() : 0)
                .sum();
        articleData.put("收藏", articleStars);

        // 将视频和文章数据添加到结果中
        List<JSONObject> resourceData = new ArrayList<>();
        resourceData.add(videoData);
        resourceData.add(articleData);
        resultMap.put("resourceData", resourceData);

        // 总学法人数（视频观看数 + 文章观看数）
        int totalLearners = videoViews + articleViews;
        resultMap.put("totalLearners", totalLearners);

        return resultMap;
    }

    /**
     * 普法学习分页查询
     * 修改说明：
     * 1. 数据来源变更：从 LawEducationResourcesView 表查询改为直接基于 LeResources 表查询
     * 2. 区域筛选变更：从基于用户地区代码筛选改为基于资源单位（deptId）筛选
     * 3. 移除对用户浏览记录表的依赖，直接返回资源信息
     * 4. 保持分页结构和返回字段不变
     *
     * @param areaName 地区名称
     * @return 包含资源信息的分页结果
     */
    @Override
    public PageResult<JSONObject> lawLearnPage(String areaName) {
        // 获取指定地区的单位ID列表（基于资源单位进行区域筛选）
        Set<String> areaDeptIds = new HashSet<>();
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 根据地区名称获取对应的组织机构ID
            String orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                // 获取该组织机构及其下级机构的所有ID
                areaDeptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 直接查询资源表，不再依赖用户浏览记录
        QueryWrapper<LeResources> resourceQueryWrapper = new QueryWrapper<>();

        // 只查询有效资源
        resourceQueryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6);

        // 根据地区筛选资源单位
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            if (CollectionUtil.isNotEmpty(areaDeptIds)) {
                resourceQueryWrapper.lambda().in(LeResources::getDeptId, areaDeptIds);
            } else {
                // 如果指定了地区但没有找到对应的单位，则返回空结果
                return new PageResult<>(new Page<>());
            }
        }

        // 按创建时间降序排序（模拟浏览时间排序）
        resourceQueryWrapper.lambda().orderByDesc(LeResources::getCreateTime);

        // 执行分页查询
        Page<LeResources> page = PageFactory.defaultPage();
        Page<LeResources> resourcePage = leResourcesService.page(page, resourceQueryWrapper);

        // 处理查询结果
        List<JSONObject> resultList = new ArrayList<>();
        for (LeResources resource : resourcePage.getRecords()) {
            JSONObject jsonObject = new JSONObject();

            // 资源基本信息
            jsonObject.put("resourceId", resource.getId());
            jsonObject.put("resourceName", resource.getTitle());
            jsonObject.put("resourceType", resource.getType());
            jsonObject.put("deptName", resource.getDeptName());  // 共享单位

            // 资源类型名称
            String typeName = "其他";
            if (resource.getType() != null) {
                if (resource.getType().startsWith("1") || resource.getType().startsWith("2")) {
                    typeName = "普法文章";
                } else if (resource.getType().startsWith("3") || resource.getType().startsWith("4")) {
                    typeName = "普法视频";
                }
            }
            jsonObject.put("typeName", typeName);

            // 添加资源级别信息
            String resourceLevel = "省级";
            if (ObjectUtil.isNotEmpty(resource.getSource())) {
                if ("0".equals(resource.getSource())) {
                    resourceLevel = "省级";
                } else if ("1".equals(resource.getSource())) {
                    resourceLevel = "市级";
                } else if ("2".equals(resource.getSource())) {
                    resourceLevel = "区县级";
                }
            }
            jsonObject.put("resourceLevel", resourceLevel);

            // 添加管辖单位信息
            jsonObject.put("manageDept", resource.getDeptName());

            // 添加地点信息（如果有）
            String location = "";
            // 尝试从deptName获取地点信息
            if (StringUtils.isNotBlank(resource.getDeptName())) {
                location = resource.getDeptName();
            }
            jsonObject.put("location", location);

            // 添加统计信息（从LeResources表直接获取）
            jsonObject.put("viewNum", resource.getViewNum() != null ? resource.getViewNum() : 0);
            jsonObject.put("starNum", resource.getStarNum() != null ? resource.getStarNum() : 0);
            jsonObject.put("likesNum", resource.getLikesNum() != null ? resource.getLikesNum() : 0);

            // 由于不再依赖用户浏览记录，移除用户相关信息，保持字段兼容性
            jsonObject.put("userId", "");
            jsonObject.put("userName", "");
            jsonObject.put("userPhone", "");
            jsonObject.put("userOrg", "");
            jsonObject.put("userLevel", "");
            jsonObject.put("userLocation", "");

            // 资源时间信息（使用资源创建时间替代浏览时间）
            jsonObject.put("viewTime", resource.getCreateTime());  // 资源创建时间
            jsonObject.put("isStar", false);  // 由于不再依赖用户记录，默认为false

            resultList.add(jsonObject);
        }

        // 创建PageResult对象
        PageResult<JSONObject> pageResult = new PageResult<>();
        pageResult.setRows(resultList);
        pageResult.setPageNo((int) resourcePage.getCurrent());
        pageResult.setPageSize((int) resourcePage.getSize());
        pageResult.setTotalRows((int) resourcePage.getTotal());

        return pageResult;
    }

    @Override
    public Map<String, Object> lawTest(String areaName) {
        return bigScreenService.lawTest(areaName);
    }

    @Override
    public PageResult<JSONObject> lawTestPage(String areaName, Integer type) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 全省统计，使用原来的查询方式
            page = bigScreenMapper.lawTestPageWithPagination(page, type, null);
        } else {
            // 获取区域编码
            String areaCode = getAreaCodeByName(areaName);

            if (ObjectUtil.isNotEmpty(areaCode)) {
                // 获取包含本级和下级的所有区域代码
                Set<String> areaCodes = cityAreaInfoService.getAreaCodes(areaCode);

                if (CollectionUtil.isNotEmpty(areaCodes)) {
                    // 对于 exam_result 表，需要先获取区域名称
                    List<String> areaNamesList = cityAreaInfoMapper.getAreaNamesByIds(areaCodes);
                    Set<String> areaNames = CollectionUtil.isNotEmpty(areaNamesList) ? new HashSet<>(areaNamesList) : Collections.emptySet();

                    // 使用新的范围查询方法
                    page = bigScreenMapper.lawTestPageWithPaginationByAreaCodes(page, type, areaCodes, areaNames);
                } else {
                    // 如果没有找到下级区域，使用原来的模糊查询方式
                    page = bigScreenMapper.lawTestPageWithPagination(page, type, areaCode);
                }
            } else {
                // 如果没有找到区域编码，使用原来的查询方式
                page = bigScreenMapper.lawTestPageWithPagination(page, type, null);
            }
        }

        List<JSONObject> records = page.getRecords();

        //创建时间格式化
        for (JSONObject record : records) {
            Date createTime = record.getDate("create_time");
            if (createTime != null) {
                record.put("create_time", DateUtil.format(createTime, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public Map<String, Object> lawNews(String areaName) {
        Map<String, Object> resultMap = new HashMap<>();

        // 获取区域代码和组织机构ID
        String areaCode = null;
        String orgId = null;
        Set<String> deptIds = null;

        if (ObjectUtil.isNotEmpty(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaCode = getAreaCodeByName(areaName);
            orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                deptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 查询所有父标签
        QueryWrapper<LerLabel> parentLabelQuery = new QueryWrapper<>();
        parentLabelQuery.lambda().isNull(LerLabel::getPid);
        parentLabelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
        List<LerLabel> parentLabels = lerLabelService.list(parentLabelQuery);

        // 创建父标签统计列表
        List<JSONObject> parentLabelStats = new ArrayList<>();
        // 总数统计
        int totalCount = 0;

        for (LerLabel parentLabel : parentLabels) {
            // 查询所有子标签
            QueryWrapper<LerLabel> childLabelQuery = new QueryWrapper<>();
            childLabelQuery.lambda().eq(LerLabel::getPid, parentLabel.getId());
            childLabelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
            List<LerLabel> childLabels = lerLabelService.list(childLabelQuery);

            // 查询父标签关联的所有资源数量
            List<String> labelIdList = new ArrayList<>();
            labelIdList.add(parentLabel.getId());

            // 子标签ID列表
            if (CollectionUtil.isNotEmpty(childLabels)) {
                childLabels.forEach(childLabel -> labelIdList.add(childLabel.getId()));
            }

            // 统计标签关联的资源总数，使用Set去重避免重复计算
            Set<String> allResourceIds = new HashSet<>();
            if (CollectionUtil.isNotEmpty(labelIdList)) {
                for (String labelId : labelIdList) {
                    List<String> resourceIds = ((LerLabelMapper) lerLabelService.getBaseMapper()).selectResContact(labelId);
                    if (CollectionUtil.isNotEmpty(resourceIds)) {
                        allResourceIds.addAll(resourceIds); // 添加到Set中自动去重
                    }
                }
            }

            int count = 0;
            if (CollectionUtil.isNotEmpty(allResourceIds)) {
                // 按区域过滤资源
                if (ObjectUtil.isNotEmpty(deptIds)) {
                    // 查询这些资源中属于指定区域的数量
                    QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                    resourceQuery.lambda()
                            .in(LeResources::getId, allResourceIds)
                            .eq(LeResources::getSource, 0)
                            .in(LeResources::getDeptId, deptIds)
                            .in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源
                    count = leResourcesService.count(resourceQuery);
                } else {
                    // 不需要按区域过滤，但需要过滤状态
                    QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                    resourceQuery.lambda()
                            .in(LeResources::getId, allResourceIds)
                            .eq(LeResources::getSource, 0)
                            .in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源
                    count = leResourcesService.count(resourceQuery);
                }
            }

            // 添加到统计结果中
            JSONObject parentStat = new JSONObject();
            parentStat.put("name", parentLabel.getName());
            parentStat.put("value", count);
            parentLabelStats.add(parentStat);

            totalCount += count;
        }

        // 查询所有子标签统计数据
        List<JSONObject> childLabelStats = new ArrayList<>();

        // 针对每个父标签，查询其下的子标签统计
        for (LerLabel parentLabel : parentLabels) {
            QueryWrapper<LerLabel> childLabelQuery = new QueryWrapper<>();
            childLabelQuery.lambda().eq(LerLabel::getPid, parentLabel.getId());
            childLabelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
            List<LerLabel> childLabels = lerLabelService.list(childLabelQuery);

            for (LerLabel childLabel : childLabels) {
                List<String> resourceIds = ((LerLabelMapper) lerLabelService.getBaseMapper()).selectResContact(childLabel.getId());

                // 按区域过滤资源
                int count = 0;
                if (ObjectUtil.isNotEmpty(deptIds) && CollectionUtil.isNotEmpty(resourceIds)) {
                    // 查询这些资源中属于指定区域的数量
                    QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                    resourceQuery.lambda()
                            .in(LeResources::getId, resourceIds)
                            .eq(LeResources::getSource, 0) // 添加source过滤条件，保持与父标签统计一致
                            .in(LeResources::getDeptId, deptIds)
                            .in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源
                    count = leResourcesService.count(resourceQuery);
                } else if (CollectionUtil.isNotEmpty(resourceIds)) {
                    // 不需要按区域过滤，但需要过滤状态
                    QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                    resourceQuery.lambda()
                            .in(LeResources::getId, resourceIds)
                            .eq(LeResources::getSource, 0) // 添加source过滤条件，保持与父标签统计一致
                            .in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源
                    count = leResourcesService.count(resourceQuery);
                }

                if (count > 0) {  // 只显示有资源的标签
                    JSONObject childStat = new JSONObject();
                    childStat.put("name", childLabel.getName());
                    childStat.put("value", count);
                    childStat.put("parentName", parentLabel.getName());
                    childLabelStats.add(childStat);
                }
            }
        }

        // 将结果放入返回Map
        resultMap.put("totalCount", totalCount);
        resultMap.put("parentStats", parentLabelStats);
        resultMap.put("childStats", childLabelStats);

        return resultMap;
    }

    @Override
    public PageResult<JSONObject> lawNewsPage(String areaName, String labelName) {
        // 获取区域代码和组织机构ID
        String areaCode = null;
        String orgId = null;
        Set<String> deptIds = null;

        if (ObjectUtil.isNotEmpty(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaCode = getAreaCodeByName(areaName);
            orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                deptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 查询标签
        QueryWrapper<LerLabel> labelQuery = new QueryWrapper<>();
        labelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签

        // 如果指定了标签名称，进行过滤
        if (StringUtils.isNotBlank(labelName)) {
            labelQuery.lambda().like(LerLabel::getName, labelName);
        }

        List<LerLabel> labels = lerLabelService.list(labelQuery);

        // 如果没有找到标签，返回空结果
        if (CollectionUtil.isEmpty(labels)) {
            return com.concise.common.util.PageUtil.getPageResult(new ArrayList<JSONObject>());
        }

        // 收集所有标签ID
        List<String> labelIds = labels.stream().map(LerLabel::getId).collect(Collectors.toList());

        // 收集所有资源ID
        List<String> resourceIds = new ArrayList<>();
        for (String labelId : labelIds) {
            List<String> ids = ((LerLabelMapper) lerLabelService.getBaseMapper()).selectResContact(labelId);
            if (CollectionUtil.isNotEmpty(ids)) {
                resourceIds.addAll(ids);
            }
        }

        // 如果没有找到资源，返回空结果
        if (CollectionUtil.isEmpty(resourceIds)) {
            return com.concise.common.util.PageUtil.getPageResult(new ArrayList<JSONObject>());
        }

        // 查询资源详情
        QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
        resourceQuery.lambda()
                .in(LeResources::getId, resourceIds)
                .in(LeResources::getStatus, 1, 2, 5, 6); // 只查询有效资源

        // 按区域过滤
        if (ObjectUtil.isNotEmpty(deptIds)) {
            resourceQuery.lambda().in(LeResources::getDeptId, deptIds);
        }

        // 按创建时间降序排序
        resourceQuery.lambda().orderByDesc(LeResources::getCreateTime);

        // 执行分页查询
        Page<LeResources> page = PageFactory.defaultPage();
        Page<LeResources> resultPage = leResourcesService.page(page, resourceQuery);

        // 转换为JSONObject列表
        List<JSONObject> resultList = new ArrayList<>();
        for (LeResources resource : resultPage.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", resource.getId());
            jsonObject.put("title", resource.getTitle());  // 资讯标题
            jsonObject.put("deptName", resource.getDeptName());  // 单位
            jsonObject.put("createTime", DateUtil.format(resource.getCreateTime(), DatePattern.NORM_DATETIME_FORMAT));  // 提交时间

            // 获取资源关联的标签
            List<String> firstLevelLabels = lerLabelMapper.selectFirstLabelName(resource.getId());
            List<String> secondLevelLabels = lerLabelMapper.selectSecondLabelName(resource.getId());


            if (CollectionUtil.isNotEmpty(firstLevelLabels)) {
                String join = String.join(SymbolConstant.COMMA, firstLevelLabels);
                jsonObject.put("firstLevelLabels", join);
            } else {
                jsonObject.put("firstLevelLabels", "");
            }
            if (CollectionUtil.isNotEmpty(secondLevelLabels)) {
                String join = String.join(SymbolConstant.COMMA, secondLevelLabels);
                jsonObject.put("secondLevelLabels", join);
            } else {
                jsonObject.put("secondLevelLabels", "");
            }

            resultList.add(jsonObject);
        }
        Page<JSONObject> pageResult = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        pageResult.setRecords(resultList);
        // 使用PageUtil工具类创建PageResult
        return new PageResult<>(pageResult);
    }

    @Override
    public Map<String, Object> lawHot(String areaName) {
        return bigScreenService.lawHot(areaName);
    }

    @Override
    public Map<String, Object> lawReportByArea(String areaName) {
        return bigScreenService.lawReportByArea(areaName);
    }

    @Override
    public List<JSONObject> lawCulture(String areaName) {
        return bigScreenService.lawCulture(areaName);
    }

    @Override
    public PageResult<JSONObject> lawCulturePage(String areaName, Integer type, Integer positionLevel) {
        return bigScreenService.lawCulturePage(areaName, type, positionLevel);
    }

    @Override
    public Map<String, Object> lawCultureMap(String areaName, Integer type, Integer positionLevel) {
        Map<String, Object> resultMap = new HashMap<>();
        // 根据类型和位置等级获取对应的资源
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(LawPosition::getPositionType,
                LawPositionBaseEnum.TRADITIONAL_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.RED_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.CONSTITUTION_EDUCATION.getCode(),
                LawPositionBaseEnum.CIVIL_CODE_EDUCATION.getCode(),
                LawPositionBaseEnum.YOUTH_LEGAL_EDUCATION.getCode(),
                LawPositionBaseEnum.OTHER_LOCAL_LEGAL_CULTURE.getCode());
        if (ObjectUtil.isNotEmpty(type)) {
            queryWrapper.lambda().eq(LawPosition::getPositionType, type);
        }
        if (ObjectUtil.isNotEmpty(positionLevel)) {
            queryWrapper.lambda().eq(LawPosition::getPositionLevel, positionLevel);
        }
        if (ObjectUtil.isNotEmpty(areaName)) {
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                queryWrapper.lambda().eq(LawPosition::getCity, areaName);
            } else {
                queryWrapper.lambda().eq(LawPosition::getArea, areaName);
            }
        }
        List<LawPosition> positionList = lawPositionService.list(queryWrapper);

        // 将资源按区域分组显示数量,省级则看各地市分布，地市看区县
        if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
            // 省级资源分布
            List<Map<String, Object>> resourcesByArea = positionList.stream()
                    .collect(Collectors.groupingBy(LawPosition::getCity, Collectors.counting()))
                    .entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> item = new HashMap<>();
                        item.put("name", entry.getKey());
                        item.put("value", entry.getValue().intValue());
                        return item;
                    })
                    .collect(Collectors.toList());
            resultMap.put("amountMap", resourcesByArea);
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            // 地市资源分布
            List<Map<String, Object>> resourcesByArea = positionList.stream()
                    .collect(Collectors.groupingBy(LawPosition::getArea, Collectors.counting()))
                    .entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> item = new HashMap<>();
                        item.put("name", entry.getKey());
                        item.put("value", entry.getValue().intValue());
                        return item;
                    })
                    .collect(Collectors.toList());
            resultMap.put("amountMap", resourcesByArea);
        } else {
            // 区县资源分布
            List<Map<String, Object>> resourcesByArea = positionList.stream()
                    .collect(Collectors.groupingBy(LawPosition::getArea, Collectors.counting()))
                    .entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> item = new HashMap<>();
                        item.put("name", entry.getKey());
                        item.put("value", entry.getValue().intValue());
                        return item;
                    })
                    .collect(Collectors.toList());
            resultMap.put("amountMap", resourcesByArea);
        }

        resultMap.put("list", positionList);
        //全国法治文化阵地
        QueryWrapper<LawPosition> nationalWrapper = new QueryWrapper<>();
        nationalWrapper.lambda().eq(LawPosition::getPositionLevel, "1")
                .in(LawPosition::getPositionType, LawPositionBaseEnum.TRADITIONAL_LEGAL_CULTURE.getCode(), LawPositionBaseEnum.RED_LEGAL_CULTURE.getCode(), LawPositionBaseEnum.CONSTITUTION_EDUCATION.getCode(), LawPositionBaseEnum.CIVIL_CODE_EDUCATION.getCode(), LawPositionBaseEnum.YOUTH_LEGAL_EDUCATION.getCode(), LawPositionBaseEnum.OTHER_LOCAL_LEGAL_CULTURE.getCode());
        if (ObjectUtil.isNotEmpty(areaName)) {
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                nationalWrapper.lambda().eq(LawPosition::getCity, areaName);
            } else {
                nationalWrapper.lambda().eq(LawPosition::getArea, areaName);
            }
        }

        List<LawPosition> nationalList = lawPositionService.list(nationalWrapper);
        resultMap.put("nationalList", nationalList);

        return resultMap;
    }

    @Override
    public List<JSONObject> lawCultureByArea(String areaName) {
        // 按地区统计法治文化阵地分布情况
        List<JSONObject> resultList = new ArrayList<>();

        // 查询所有城市
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT city");
        if (StringUtils.isNotBlank(areaName)) {
            queryWrapper.lambda().like(LawPosition::getAddressName, areaName);
        }
        List<LawPosition> cityList = lawPositionService.list(queryWrapper);

        // 对每个城市统计全国级和省级阵地数量
        for (LawPosition position : cityList) {
            String city = position.getCity();
            if (StringUtils.isBlank(city)) {
                continue;
            }

            JSONObject cityJson = new JSONObject();
            cityJson.put("name", city);

            // 统计全国级阵地数量
            QueryWrapper<LawPosition> nationalWrapper = new QueryWrapper<>();
            nationalWrapper.lambda().eq(LawPosition::getCity, city)
                    .eq(LawPosition::getPositionLevel, "1");
            int nationalCount = lawPositionService.count(nationalWrapper);
            cityJson.put("national", nationalCount);

            // 统计省级阵地数量
            QueryWrapper<LawPosition> provinceWrapper = new QueryWrapper<>();
            provinceWrapper.lambda().eq(LawPosition::getCity, city)
                    .eq(LawPosition::getPositionLevel, "2");
            int provinceCount = lawPositionService.count(provinceWrapper);
            cityJson.put("province", provinceCount);

            // 统计总数
            cityJson.put("total", nationalCount + provinceCount);

            resultList.add(cityJson);
        }

        // 按总数排序
        resultList.sort((o1, o2) -> {
            Integer total1 = o1.getInteger("total");
            Integer total2 = o2.getInteger("total");
            return total2.compareTo(total1);
        });

        return resultList;
    }

    @Override
    public List<JSONObject> lawCultureList(String areaName) {
        // 获取法治文化阵地列表数据
        List<JSONObject> resultList = new ArrayList<>();

        // 查询法治文化阵地
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(LawPosition::getCreateTime);

        // 只查询法治文化阵地相关类型
        queryWrapper.lambda().in(LawPosition::getPositionType,
                LawPositionBaseEnum.TRADITIONAL_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.RED_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.CONSTITUTION_EDUCATION.getCode(),
                LawPositionBaseEnum.CIVIL_CODE_EDUCATION.getCode(),
                LawPositionBaseEnum.YOUTH_LEGAL_EDUCATION.getCode(),
                LawPositionBaseEnum.OTHER_LOCAL_LEGAL_CULTURE.getCode());

        // 如果指定了区域名称，进行区域过滤
        if (StringUtils.isNotBlank(areaName)) {
            queryWrapper.lambda().like(LawPosition::getAddressName, areaName);
        }

        // 限制返回数量
        Page<LawPosition> page = new Page<>(1, 10);
        Page<LawPosition> positionPage = lawPositionService.page(page, queryWrapper);

        // 转换为JSONObject列表
        for (LawPosition position : positionPage.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", position.getId());
            jsonObject.put("name", position.getPositionName());
            jsonObject.put("address", position.getAddressDetail());
            jsonObject.put("type", position.getPositionType());

            // 获取类型名称
            LawPositionBaseEnum typeEnum = LawPositionBaseEnum.getByCode(position.getPositionType());
            if (typeEnum != null) {
                jsonObject.put("typeName", typeEnum.getDescription());
            }

            // 获取级别名称
            String levelName = "未知";
            if ("1".equals(position.getPositionLevel())) {
                levelName = "全国";
            } else if ("2".equals(position.getPositionLevel())) {
                levelName = "省级";
            }
            jsonObject.put("level", position.getPositionLevel());
            jsonObject.put("levelName", levelName);

            resultList.add(jsonObject);
        }

        return resultList;
    }

    @Override
    public PageResult<JSONObject> lawCultureListPage(String areaName, Integer level) {
        // 获取法治文化阵地列表数据（分页版本）
        List<JSONObject> resultList = new ArrayList<>();

        // 查询法治文化阵地
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(LawPosition::getCreateTime);

        // 只查询法治文化阵地相关类型
        queryWrapper.lambda().in(LawPosition::getPositionType,
                LawPositionBaseEnum.TRADITIONAL_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.RED_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.CONSTITUTION_EDUCATION.getCode(),
                LawPositionBaseEnum.CIVIL_CODE_EDUCATION.getCode(),
                LawPositionBaseEnum.YOUTH_LEGAL_EDUCATION.getCode(),
                LawPositionBaseEnum.OTHER_LOCAL_LEGAL_CULTURE.getCode());

        // 如果指定了区域名称，进行区域过滤
        if (StringUtils.isNotBlank(areaName)) {
            queryWrapper.lambda().like(LawPosition::getAddressName, areaName);
        }

        // 如果指定了阵地级别，进行级别过滤
        if (level != null) {
            queryWrapper.lambda().eq(LawPosition::getPositionLevel, level.toString());
        }

        // 使用分页查询
        Page<LawPosition> page = PageFactory.defaultPage();
        Page<LawPosition> positionPage = lawPositionService.page(page, queryWrapper);

        // 转换为JSONObject列表
        for (LawPosition position : positionPage.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", position.getId());
            jsonObject.put("name", position.getPositionName());
            jsonObject.put("address", position.getAddressDetail());
            jsonObject.put("type", position.getPositionType());

            // 获取类型名称
            LawPositionBaseEnum typeEnum = LawPositionBaseEnum.getByCode(position.getPositionType());
            if (typeEnum != null) {
                jsonObject.put("typeName", typeEnum.getDescription());
            }

            // 获取级别名称
            String levelName = "未知";
            if ("1".equals(position.getPositionLevel())) {
                levelName = "全国";
            } else if ("2".equals(position.getPositionLevel())) {
                levelName = "省级";
            }
            jsonObject.put("level", position.getPositionLevel());
            jsonObject.put("levelName", levelName);

            resultList.add(jsonObject);
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) positionPage.getCurrent());
        result.setPageSize((int) positionPage.getSize());
        result.setTotalRows(Convert.toInt(positionPage.getTotal()));
        result.setTotalPage(Convert.toInt(positionPage.getPages()));
        result.setRows(resultList);
        return result;
    }

    @Override
    public List<JSONObject> lawActivityTotal(String areaName) {
        List<JSONObject> resultList = new ArrayList<>();

        // 查询各地市的法律活动数量统计
        String areaCode = null;
        if (StringUtils.isNotBlank(areaName)) {
            areaCode = getAreaCodeByName(areaName);
        }

        // 通过SQL查询获取各地市的活动统计数据
        List<Map<String, Object>> cityActivityStats = bigScreenMapper.getCityActivityStatistics(areaCode);

        // 处理查询结果，转换为前端需要的格式
        if (CollectionUtil.isNotEmpty(cityActivityStats)) {
            for (Map<String, Object> stat : cityActivityStats) {
                JSONObject json = new JSONObject();
                String cityName = (String) stat.get("city_name");
                Integer count = ((Number) stat.get("activity_count")).intValue();

                json.put("name", cityName);
                json.put("value", count);
                resultList.add(json);
            }
        } else {
            for (String s : CommonConstant.CITY_LIST) {
                JSONObject json = new JSONObject();
                json.put("name", s);
                json.put("value", 0);
                resultList.add(json);
            }
        }

        return resultList;
    }

    @Override
    public List<JSONObject> lawArt(String areaName) {
        List<JSONObject> resultList = new ArrayList<>();

        // 漫画 - 11
        JSONObject manhuaJson = new JSONObject();
        manhuaJson.put("name", "漫画");
        manhuaJson.put("value", countResourcesByTypeAndArea("11", areaName));
        manhuaJson.put("code", "11");
        resultList.add(manhuaJson);

        // 宣传画 - 12
        JSONObject xuanchuanhuaJson = new JSONObject();
        xuanchuanhuaJson.put("name", "宣传画");
        xuanchuanhuaJson.put("value", countResourcesByTypeAndArea("12", areaName));
        xuanchuanhuaJson.put("code", "12");
        resultList.add(xuanchuanhuaJson);

        // 海报 - 13
        JSONObject haibaoJson = new JSONObject();
        haibaoJson.put("name", "海报");
        haibaoJson.put("value", countResourcesByTypeAndArea("13", areaName));
        haibaoJson.put("code", "13");
        resultList.add(haibaoJson);

        // 动画 - 31
        JSONObject donghuaJson = new JSONObject();
        donghuaJson.put("name", "动画");
        donghuaJson.put("value", countResourcesByTypeAndArea("31", areaName));
        donghuaJson.put("code", "31");
        resultList.add(donghuaJson);

        // 宣传片 - 32
        JSONObject xuanchuanpianJson = new JSONObject();
        xuanchuanpianJson.put("name", "宣传片");
        xuanchuanpianJson.put("value", countResourcesByTypeAndArea("32", areaName));
        xuanchuanpianJson.put("code", "32");
        resultList.add(xuanchuanpianJson);

        // 微电影 - 33
        JSONObject weidiangyingJson = new JSONObject();
        weidiangyingJson.put("name", "微电影");
        weidiangyingJson.put("value", countResourcesByTypeAndArea("33", areaName));
        weidiangyingJson.put("code", "33");
        resultList.add(weidiangyingJson);

        // 情景剧 - 34
        JSONObject qingjingjuJson = new JSONObject();
        qingjingjuJson.put("name", "情景剧");
        qingjingjuJson.put("value", countResourcesByTypeAndArea("34", areaName));
        qingjingjuJson.put("code", "34");
        resultList.add(qingjingjuJson);

        // 视频课件 - 41
        JSONObject videoJson = new JSONObject();
        videoJson.put("name", "视频课件");
        videoJson.put("value", countResourcesByTypeAndArea("41", areaName));
        videoJson.put("code", "41");
        resultList.add(videoJson);

        // 文档课件 - 42
        JSONObject docJson = new JSONObject();
        docJson.put("name", "文档课件");
        docJson.put("value", countResourcesByTypeAndArea("42", areaName));
        docJson.put("code", "42");
        resultList.add(docJson);

        // 公益广告 - 1
        JSONObject adJson = new JSONObject();
        adJson.put("name", "公益广告");
        adJson.put("value", countResourcesByTypeAndArea("1", areaName));
        adJson.put("code", "1");
        resultList.add(adJson);

        // 计算所有文艺库资源总数
        int total = resultList.stream().mapToInt(item -> item.getInteger("value")).sum();

        // 添加总计数据
        JSONObject totalJson = new JSONObject();
        totalJson.put("name", "法治文艺");
        totalJson.put("value", total);
        totalJson.put("code", "total");
        resultList.add(0, totalJson);

        return resultList;
    }

    /**
     * 根据资源类型和区域统计资源数量
     *
     * @param type     资源类型
     * @param areaName 区域名称
     * @return 资源数量
     */
    private int countResourcesByTypeAndArea(String type, String areaName) {
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(LeResources::getType, type);

        // 区域筛选
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            queryWrapper.lambda().like(LeResources::getDeptName, areaName);
        }

        // 有效状态的资源 (1已通过,2已通过且被合并,5省市直接新增,6省厅采用)
        queryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6);

        return leResourcesService.count(queryWrapper);
    }

    @Override
    public PageResult<JSONObject> lawArtPage(String areaName, Integer type) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawArtPage(page, String.valueOf(type), areaName);
        List<JSONObject> records = page.getRecords();

        // 处理每条记录的标签
        for (JSONObject record : records) {
            String resourceId = record.getString("id");
            if (StrUtil.isNotEmpty(resourceId)) {
                // 通过关联表查询标签ID列表
                List<String> labelIds = lerLabelService.selectLabelContact(resourceId);
                if (CollectionUtil.isNotEmpty(labelIds)) {
                    List<String> labelNames = new ArrayList<>();
                    for (String labelId : labelIds) {
                        LerLabel label = lerLabelService.getById(labelId);
                        if (label != null && StringUtils.isNotEmpty(label.getName())) {
                            labelNames.add(label.getName());
                        }
                    }
                    record.put("labelNames", labelNames);
                }
            }

            // 确保所有必要字段存在
            if (!record.containsKey("theme_type") || record.getString("theme_type") == null) {
                record.put("theme_type", "");
            }

            if (!record.containsKey("audience_type") || record.getString("audience_type") == null) {
                record.put("audience_type", "");
            }

            //处理图片和文字
            record.put("fileList", sysFileInfoService.lambdaQuery().eq(SysFileInfo::getReferId, record.getString("id")).list());

        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public List<JSONObject> lawArtCategory(String areaName) {
        return bigScreenService.lawArtCategory(areaName);
    }

    @Override
    public PageResult<JSONObject> lawArtCategoryPage(String areaName, String labelName) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawArtCategoryPage(page, labelName, areaName);
        List<JSONObject> records = page.getRecords();

        // 处理每条记录的标签
        for (JSONObject record : records) {
            String resourceId = record.getString("id");
            if (StrUtil.isNotEmpty(resourceId)) {
                // 通过关联表查询标签ID列表
                List<String> labelIds = lerLabelService.selectLabelContact(resourceId);
                if (CollectionUtil.isNotEmpty(labelIds)) {
                    List<String> labelNames = new ArrayList<>();
                    for (String labelId : labelIds) {
                        LerLabel label = lerLabelService.getById(labelId);
                        if (label != null && StringUtils.isNotEmpty(label.getName())) {
                            labelNames.add(label.getName());
                        }
                    }
                    record.put("labelNames", labelNames);
                }
            }

            // 确保所有必要字段存在
            if (!record.containsKey("theme_type") || record.getString("theme_type") == null) {
                record.put("theme_type", "");
            }

            if (!record.containsKey("audience_type") || record.getString("audience_type") == null) {
                record.put("audience_type", "");
            }
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public List<JSONObject> lawArtGroup(String areaName) {
        List<JSONObject> resultList = new ArrayList<>();

        // 查询所有有效的资源，按面向群体分类统计
        QueryWrapper<LeResources> baseQueryWrapper = new QueryWrapper<>();

        // 有效状态的资源
        baseQueryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6);

        // 只统计普法资源库的数据（source = "0"）
        baseQueryWrapper.lambda().eq(LeResources::getSource, "0");

        // 区域筛选
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            baseQueryWrapper.lambda().like(LeResources::getDeptName, areaName);
        }

        // 获取所有符合条件的资源
        List<LeResources> allResources = leResourcesService.list(baseQueryWrapper);

        // 按面向群体分类统计（支持多选）
        Map<String, Integer> audienceCountMap = new HashMap<>();

        for (LeResources resource : allResources) {
            String audienceType = resource.getAudienceType();

            // 如果面向群体分类为空，归类为"其他"
            if (StringUtils.isBlank(audienceType)) {
                audienceCountMap.put("其他", audienceCountMap.getOrDefault("其他", 0) + 1);
            } else {
                // 处理多选情况，按逗号分隔
                String[] audienceTypes = audienceType.split(",");
                for (String singleAudience : audienceTypes) {
                    String trimmedAudience = singleAudience.trim();
                    if (StringUtils.isNotBlank(trimmedAudience)) {
                        audienceCountMap.put(trimmedAudience, audienceCountMap.getOrDefault(trimmedAudience, 0) + 1);
                    }
                }
            }
        }

        // 获取字典值对应的显示名称（如果有pfzy-mxrq字典的话）
        for (Map.Entry<String, Integer> entry : audienceCountMap.entrySet()) {
            JSONObject groupJson = new JSONObject();
            String audienceCode = entry.getKey();
            Integer count = entry.getValue();

            // 尝试获取字典显示名称，如果获取失败则使用原始值
            String audienceName = getDictDisplayName("pfzy-mxrq", audienceCode);
            if (StringUtils.isBlank(audienceName)) {
                audienceName = audienceCode;
            }

            groupJson.put("name", audienceName);
            groupJson.put("value", count);
            resultList.add(groupJson);
        }

        // 过滤掉数量为0的群体
        resultList = resultList.stream()
                .filter(json -> json.getInteger("value") > 0)
                .collect(Collectors.toList());

        // 按数量从大到小排序
        resultList.sort((o1, o2) -> o2.getInteger("value").compareTo(o1.getInteger("value")));

        return resultList;
    }

    /**
     * 获取字典显示名称
     *
     * @param dictTypeCode 字典类型编码
     * @param dictCode     字典编码
     * @return 字典显示名称
     */
    private String getDictDisplayName(String dictTypeCode, String dictCode) {
        try {
            if (StringUtils.isBlank(dictCode)) {
                return "";
            }

            // 查询字典类型
            QueryWrapper<SysDictType> typeQueryWrapper = new QueryWrapper<>();
            typeQueryWrapper.lambda().eq(SysDictType::getCode, dictTypeCode)
                    .eq(SysDictType::getStatus, 0);
            SysDictType dictType = sysDictTypeService.getOne(typeQueryWrapper);

            if (dictType == null) {
                return "";
            }

            // 查询字典数据
            QueryWrapper<SysDictData> dataQueryWrapper = new QueryWrapper<>();
            dataQueryWrapper.lambda().eq(SysDictData::getTypeId, dictType.getId())
                    .eq(SysDictData::getCode, dictCode)
                    .eq(SysDictData::getStatus, 0);
            SysDictData dictData = sysDictDataService.getOne(dataQueryWrapper);

            return dictData != null ? dictData.getValue() : "";
        } catch (Exception e) {
            log.warn("获取字典显示名称失败: dictTypeCode={}, dictCode={}", dictTypeCode, dictCode, e);
            return "";
        }
    }

    @Override
    public void lawResourceTag() {
        bigScreenService.lawResourceTag();
    }

    @Override
    public Map<String, Object> lawCultivateAnalyse(String areaName) {
        return bigScreenService.lawCultivateAnalyse(areaName);
    }

    @Override
    public List<JSONObject> lawCultivateAnalyseArea(String areaName, Integer type) {
        return bigScreenService.lawCultivateAnalyseArea(areaName, type);
    }

    @Override
    public Map<String, Object> lawVillage(String areaName) {
        Map<String, Object> map = new HashMap<>();
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                //省级民主法治示范村数量
                jsonObject.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, "1", "4").eq(LawPosition::getCity, city).eq(LawPosition::getPositionLevel, 2)));
                //国家级民主法治示范村数量
                jsonObject.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, "1", "4").eq(LawPosition::getCity, city).eq(LawPosition::getPositionLevel, 1)));
                jsonObjectList.add(jsonObject);
            }
            map.put("list", jsonObjectList);
            return map;
        } else {
            if (CommonConstant.CITY_LIST.contains(areaName)) {
                //地市下的区县，全国和省级各有多少
                List<JSONObject> jsonObjectList = new ArrayList<>();
                List<String> childAreaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
                for (String area : childAreaNameList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", area);
                    //省级民主法治示范村数量
                    jsonObject.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, "1", "4").eq(LawPosition::getArea, area).eq(LawPosition::getPositionLevel, 2)));
                    //国家级民主法治示范村数量
                    jsonObject.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, "1", "4").eq(LawPosition::getArea, area).eq(LawPosition::getPositionLevel, 1)));
                    jsonObjectList.add(jsonObject);
                }
                map.put("list", jsonObjectList);
                return map;
            }
        }
        return null;
    }

    @Override
    public Map<String, Object> lawVillageTotal(String areaName) {
        Map<String, Object> map = new HashMap<>();
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            //全国级数量
            map.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getPositionLevel, 1)));
            //省级数量
            map.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getPositionLevel, 2)));
            //各地市数量
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                jsonObject.put("value", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getCity, city)));
                jsonObjectList.add(jsonObject);
            }
            map.put("list", jsonObjectList);
        } else {
            //地市
            if (CommonConstant.CITY_LIST.contains(areaName)) {
                //地市下的区县，全国和省级各有多少
                //国家级数量
                map.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getCity, areaName).eq(LawPosition::getPositionLevel, 1)));
                //省级数量
                map.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getCity, areaName).eq(LawPosition::getPositionLevel, 2)));
                List<JSONObject> jsonObjectList = new ArrayList<>();
                List<String> childAreaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
                for (String area : childAreaNameList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", area);
                    jsonObject.put("value", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getArea, area)));
                    jsonObjectList.add(jsonObject);
                }
                map.put("list", jsonObjectList);
            } else {
                //区县
                map.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getArea, areaName).eq(LawPosition::getPositionLevel, 1)));
                map.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getArea, areaName).eq(LawPosition::getPositionLevel, 2)));
                // 显示区县自己的数据
                List<JSONObject> jsonObjectList = new ArrayList<>();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", areaName);
                jsonObject.put("value", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getArea, areaName)));
                jsonObjectList.add(jsonObject);
                map.put("list", jsonObjectList);
                //返回具体点位列表
                List<LawPosition> lawPositionList = lawPositionService.list(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getArea, areaName));
                map.put("positionList", lawPositionList);

            }

        }

        return map;
    }

    @Override
    public Map<String, Object> lawObservatory(String areaName) {
        Map<String, Object> map = new HashMap<>();
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            //省级数量
            map.put("province", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getPointLevel, 1).eq(ObPointInfo::getStatus, 4)));
            //市级数量
            map.put("city", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getPointLevel, 2).eq(ObPointInfo::getStatus, 4)));
            //区级数量
            map.put("area", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getPointLevel, 3).eq(ObPointInfo::getStatus, 4)));
            //各地市数量
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                jsonObject.put("value", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getCity, city).eq(ObPointInfo::getStatus, 4)));
                jsonObjectList.add(jsonObject);
            }
            map.put("list", jsonObjectList);
            return map;

        } else {
            //地市查区县的
            if (CommonConstant.CITY_LIST.contains(areaName)) {
                //地市下的区县，全国和省级各有多少
                //省级数量
                map.put("province", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getCity, areaName).eq(ObPointInfo::getPointLevel, 1).eq(ObPointInfo::getStatus, 4)));
                //市级数量
                map.put("city", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getCity, areaName).eq(ObPointInfo::getPointLevel, 2).eq(ObPointInfo::getStatus, 4)));
                //区级数量
                map.put("area", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getCity, areaName).eq(ObPointInfo::getPointLevel, 3).eq(ObPointInfo::getStatus, 4)));
                List<JSONObject> jsonObjectList = new ArrayList<>();
                List<String> childAreaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
                for (String area : childAreaNameList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", area);
                    jsonObject.put("value", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, area).eq(ObPointInfo::getStatus, 4)));
                    jsonObjectList.add(jsonObject);
                }
                map.put("list", jsonObjectList);
                return map;
            } else {
                //区县查自己的
                map.put("province", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, areaName).eq(ObPointInfo::getPointLevel, 1).eq(ObPointInfo::getStatus, 4)));
                map.put("city", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, areaName).eq(ObPointInfo::getPointLevel, 2).eq(ObPointInfo::getStatus, 4)));
                map.put("area", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, areaName).eq(ObPointInfo::getPointLevel, 3).eq(ObPointInfo::getStatus, 4)));
                // 显示区县自己的数据
                List<JSONObject> jsonObjectList = new ArrayList<>();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", areaName);
                jsonObject.put("value", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, areaName).eq(ObPointInfo::getStatus, 4)));
                jsonObjectList.add(jsonObject);
                map.put("list", jsonObjectList);
                return map;
            }
        }
    }

    @Override
    public PageResult<?> lawCultivateAnalysePage(String areaName, Integer type) {
        String areaCode = getAreaCodeByName(areaName);

        // 根据类型获取不同的数据
        if (type == null) {
            type = 1; // 默认为普法干部
        }

        // 创建分页对象
        Page<LawUserVo> page = PageFactory.defaultPage();

        // 1-普法干部 2-法律明白人 3-普法志愿者
        switch (type) {
            case 1:
                // 普法干部数据 - 使用数据库分页
                if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                    page = bigScreenMapper.lawCultivateUserPageWithPagination(page, null);
                } else {
                    String orgId = getOrgIdByName(areaName);
                    if (ObjectUtil.isNotEmpty(orgId)) {
                        page = bigScreenMapper.lawCultivateUserPageWithPagination(page, areaCode);
                    }
                }
                break;
            case 2:
                // 法律明白人数据 - 使用数据库分页
                if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                    page = bigScreenMapper.lawLearnerUserPageWithPagination(page, null);
                } else {
                    String orgId = getOrgIdByName(areaName);
                    if (ObjectUtil.isNotEmpty(orgId)) {
                        page = bigScreenMapper.lawLearnerUserPageWithPagination(page, areaCode);
                    }
                }
                break;
            case 3:
                // 普法志愿者数据
                // TODO: 实现普法志愿者数据查询
                break;
            default:
                break;
        }

        // 创建分页结果对象
        PageResult<LawUserVo> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(page.getRecords());
        return result;
    }

    @Override
    public PageResult<JSONObject> lawCultivateAnalyseAreaUserPage(String areaName, Integer type) {
        String areaCode = getAreaCodeByName(areaName);

        // 创建分页对象
        Page<LawUserVo> page = PageFactory.defaultPage();

        // 根据区域和类型获取对应的人员列表
        // 1-普法干部 2-法律明白人 3-普法志愿者
        if (type == 1) {
            // 获取普法干部人员列表 - 使用数据库分页
            page = bigScreenMapper.lawCultivateUserPageWithPagination(page, areaCode);
        } else if (type == 2) {
            // 获取法律明白人人员列表 - 使用数据库分页
            page = bigScreenMapper.lawLearnerUserPageWithPagination(page, areaCode);
        } else if (type == 3) {
            // TODO: 普法志愿者数据查询
        }

        // 将LawUserVo转换为JSONObject
        List<JSONObject> jsonDataList = new ArrayList<>();
        for (LawUserVo user : page.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", user.getId());
            jsonObject.put("name", user.getName());

            // 正确处理sex字段
            String sexStr = "未知";
            if (user.getSex() != null) {
                if (user.getSex() instanceof Integer) {
                    Integer sexInt = (Integer) user.getSex();
                    sexStr = sexInt == 1 ? "男" : (sexInt == 2 ? "女" : "未知");
                } else if (user.getSex() instanceof String) {
                    String sexVal = (String) user.getSex();
                    sexStr = "1".equals(sexVal) ? "男" : ("2".equals(sexVal) ? "女" : sexVal);
                }
            }
            jsonObject.put("sex", sexStr);

            jsonObject.put("phone", user.getPhone());
            jsonObject.put("orgName", user.getOrgName());
            jsonDataList.add(jsonObject);
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(jsonDataList);
        return result;
    }

    @Override
    public PageResult<JSONObject> lawVillagePage(String areaName) {
        // 使用分页查询，不再遍历每个地市
        Page<LawPosition> page = PageFactory.defaultPage();
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(LawPosition::getPositionType, "1", "4");

        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 省级查询，不需要额外条件
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            // 地市级查询
            queryWrapper.lambda().eq(LawPosition::getCity, areaName);
        } else {
            // 区县级查询
            queryWrapper.lambda().eq(LawPosition::getArea, areaName);
        }

        // 执行分页查询
        Page<LawPosition> positionPage = lawPositionService.page(page, queryWrapper);

        // 转换为JSONObject列表
        List<JSONObject> resultList = new ArrayList<>();
        for (LawPosition position : positionPage.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", position.getId());
            jsonObject.put("name", position.getPositionName());
            jsonObject.put("city", position.getCity());
            jsonObject.put("area", position.getArea());
            jsonObject.put("addressDetail", position.getAddressDetail());
            jsonObject.put("positionLevel", position.getPositionLevel());
            jsonObject.put("orgName", position.getOrgName());
            jsonObject.put("levelName", "1".equals(position.getPositionLevel()) ? "国家级" : "省级");
            resultList.add(jsonObject);
        }

        // 使用正确的构造函数创建PageResult，明确指定泛型类型
        return new PageResult<JSONObject>((Page<JSONObject>) (Page<?>) positionPage, resultList);
    }

    @Override
    public PageResult<JSONObject> lawVillageTotalPage(String areaName, Integer level) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawVillageTotalPageWithPagination(page, areaName, level);
        List<JSONObject> records = page.getRecords();

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public PageResult<BenchmarkManage> citizenLegalLiteracyPage(String areaName, Integer type) {
        QueryWrapper<BenchmarkManage> queryWrapper = new QueryWrapper<>();

        // 根据版本类型筛选
        if (ObjectUtil.isNotEmpty(type)) {
            queryWrapper.lambda().eq(BenchmarkManage::getVersionType, type);
        }

        // 根据区域名称筛选
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            String areaCode = getAreaCodeByName(areaName);
            if (StringUtils.isNotBlank(areaCode)) {
                queryWrapper.lambda().like(BenchmarkManage::getAreaCode, areaCode);
            }

//            if (CommonConstant.CITY_LIST.contains(areaName)) {
//                queryWrapper.lambda().eq(BenchmarkManage::getCity, areaName);
//            } else {
//                queryWrapper.lambda().eq(BenchmarkManage::getArea, areaName);
//            }
        }

        // 按创建时间降序排序
        queryWrapper.lambda().orderByDesc(BenchmarkManage::getCreateTime);

        // 执行分页查询
        Page<BenchmarkManage> page = PageFactory.defaultPage();
        Page<BenchmarkManage> resultPage = benchmarkManageService.page(page, queryWrapper);

        return new PageResult<>(resultPage);
    }

    /**
     * 为点位添加图片和视频信息
     *
     * @param lawPosition
     * @return
     */
    private LawPosition addMediaInfo(LawPosition lawPosition) {
        // 添加视频信息
        if (ObjectUtil.isNotEmpty(lawPosition.getVideoId())) {
            if (lawPosition.getVideoId().contains(SymbolConstant.COMMA)) {
                String[] split = lawPosition.getVideoId().split(SymbolConstant.COMMA);
                List<SysFileInfo> list = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(split)));
                lawPosition.setVideoList(list);
            } else {
                SysFileInfo sysFileInfo = sysFileInfoService.getById(lawPosition.getVideoId());
                if (ObjectUtil.isNotNull(sysFileInfo)) {
                    List<SysFileInfo> list = new ArrayList<>();
                    list.add(sysFileInfo);
                    lawPosition.setVideoList(list);
                }
            }
        }

        // 添加图片信息
        if (ObjectUtil.isNotEmpty(lawPosition.getPicId())) {
            if (lawPosition.getPicId().contains(SymbolConstant.COMMA)) {
                String[] split = lawPosition.getPicId().split(SymbolConstant.COMMA);
                List<SysFileInfo> list = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(split)));
                lawPosition.setPicList(list);
            } else {
                SysFileInfo sysFileInfo = sysFileInfoService.getById(lawPosition.getPicId());
                if (ObjectUtil.isNotNull(sysFileInfo)) {
                    List<SysFileInfo> list = new ArrayList<>();
                    list.add(sysFileInfo);
                    lawPosition.setPicList(list);
                }
            }
        }

        return lawPosition;
    }

    /**
     * 根据区域名称获取区域编码
     *
     * @param areaName
     * @return
     */
    private String getAreaCodeByName(String areaName) {
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            return CommonConstant.TOP_AREA_CODE;
        }
        return cityAreaInfoMapper.getIdByName(areaName);
    }

    /**
     * 根据区域名称获取sys_org
     *
     * @param areaName
     * @return
     */
    private String getOrgIdByName(String areaName) {
        String areaId = cityAreaInfoMapper.getIdByName(areaName);
        if (areaId != null) {
            return sysOrgService.getIdByAreaId(areaId);
        }
        return null;
    }

    @Override
    public PageResult<ObPointInfo> lawObservatoryPage(String areaName, Integer level) {
        Page<ObPointInfo> page = PageFactory.defaultPage();
        QueryWrapper<ObPointInfo> queryWrapper = new QueryWrapper<>();

        // 添加状态过滤条件，与lawObservatory方法保持一致
        queryWrapper.lambda().eq(ObPointInfo::getStatus, 4);

        if (ObjectUtil.isNotEmpty(level)) {
            queryWrapper.lambda().eq(ObPointInfo::getPointLevel, level);
        }

        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            page = obPointInfoService.page(page, queryWrapper);
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            // 使用城市字段进行精确匹配，与lawObservatory方法保持一致
            queryWrapper.lambda().eq(ObPointInfo::getCity, areaName);
            page = obPointInfoService.page(page, queryWrapper);
        } else {
            // 使用区域字段进行精确匹配，与lawObservatory方法保持一致
            queryWrapper.lambda().eq(ObPointInfo::getArea, areaName);
            page = obPointInfoService.page(page, queryWrapper);
        }

        //处理图片
        List<ObPointInfo> records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            records.forEach(item -> {
                item.setPointImgInfoList(sysFileInfoService.lambdaQuery().eq(SysFileInfo::getReferId, item.getId()).list());
            });
        }

        return new PageResult<ObPointInfo>(page);
    }

    @Override
    public List<JSONObject> lawThemeMonth(String areaName) {
        // 查询主题宣传月标签
        LerLabelParam labelParam = new LerLabelParam();
        labelParam.setPid("1"); // 主题类标签的父ID
        labelParam.setStatus(1); // 已审核通过的标签
        List<LerLabel> themeLabels = lerLabelService.list(labelParam);

        // 查找"主题宣传月"标签
        String themeMonthLabelId = null;
        for (LerLabel label : themeLabels) {
            if (label.getName().contains("主题宣传月")) {
                themeMonthLabelId = label.getId();
                break;
            }
        }

        if (themeMonthLabelId == null) {
            return new ArrayList<>(); // 如果没有找到主题宣传月标签，返回空列表
        }

        // 按地区统计主题宣传月资源数量
        return bigScreenMapper.lawThemeMonthStatistics(themeMonthLabelId, areaName);
    }

    @Override
    public PageResult<JSONObject> lawThemeMonthPage(String areaName) {
        // 查询主题宣传月标签
        LerLabelParam labelParam = new LerLabelParam();
        labelParam.setPid("1"); // 主题类标签的父ID
        labelParam.setStatus(1); // 已审核通过的标签
        List<LerLabel> themeLabels = lerLabelService.list(labelParam);

        // 查找"主题宣传月"标签
        String themeMonthLabelId = null;
        for (LerLabel label : themeLabels) {
            if (label.getName().contains("主题宣传月")) {
                themeMonthLabelId = label.getId();
                break;
            }
        }

        if (themeMonthLabelId == null) {
            return new PageResult<>(new Page<>()); // 如果没有找到主题宣传月标签，返回空结果
        }

        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawThemeMonthPageWithPagination(page, themeMonthLabelId, areaName);
        List<JSONObject> records = page.getRecords();

        // 处理标签信息
        if (CollectionUtil.isNotEmpty(records)) {
            for (JSONObject item : records) {
                // 处理标签ID转换为标签名称
                String resourceId = item.getString("id");
                if (StringUtils.isNotEmpty(resourceId)) {
                    // 通过关联表查询标签ID列表
                    List<String> labelIds = lerLabelService.selectLabelContact(resourceId);
                    if (CollectionUtil.isNotEmpty(labelIds)) {
                        List<String> labelNames = new ArrayList<>();
                        for (String labelId : labelIds) {
                            LerLabel label = lerLabelService.getById(labelId);
                            if (label != null && StringUtils.isNotEmpty(label.getName())) {
                                labelNames.add(label.getName());
                            }
                        }
                        if (CollectionUtil.isNotEmpty(labelNames)) {
                            item.put("label_names", String.join(",", labelNames));
                        }
                    }
                }

                // 确保所有必要字段存在
                if (!item.containsKey("theme_type") || item.getString("theme_type") == null) {
                    item.put("theme_type", "");
                }

                if (!item.containsKey("audience_type") || item.getString("audience_type") == null) {
                    item.put("audience_type", "");
                }

                // 格式化日期
                if (item.containsKey("create_time")) {
                    Date createTime = item.getDate("create_time");
                    if (createTime != null) {
                        item.put("share_time", DateUtil.formatDateTime(createTime));
                    }
                }
            }
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }


    @Override
    public PageResult<JSONObject> lawCultivateAnalyseAreaPage(String areaName, Integer type) {
        List<JSONObject> dataList = new ArrayList<>();

        // 1-普法干部 2-法律明白人 3-普法志愿者
        if (type == 1) {
            // 普法干部地市区县分布
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                List<JSONObject> jsonObjectList = bigScreenMapper.lawCultivateAnalyseProvince();
                // 按照地市顺序排
                for (String city : CommonConstant.CITY_LIST) {
                    for (JSONObject jsonObject : jsonObjectList) {
                        if (city.equals(jsonObject.getString("city"))) {
                            dataList.add(jsonObject);
                            break;
                        }
                    }
                }
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                // 地市
                dataList = bigScreenMapper.lawCultivateAnalyseCity(areaName);
            } else {
                // 区县
                dataList = bigScreenMapper.lawCultivateAnalyseArea(areaName);
            }
        } else if (type == 2) {
            // 法律明白人地市分布
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                dataList = bigScreenMapper.lawUnderstandProvince();
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                // 地市
                dataList = bigScreenMapper.lawUnderstandCity(areaName);
            } else {
                // 区县
                dataList = bigScreenMapper.lawUnderstandArea(areaName);
            }
        }
        // 使用PageUtil工具类进行分页处理
        return com.concise.common.util.PageUtil.getPageResult(dataList);
    }

    @Override
    public int lawLearnerTotal(String areaName) {
        String areaCode = getAreaCodeByName(areaName);
        if (ObjectUtil.isNotEmpty(areaCode)) {
            return bigScreenMapper.lawLearnerTotal(areaCode);
        }
        return 0;
    }

    @Override
    public List<LawPosition> lawVillageFeature(String areaName) {
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            List<LawPosition> list = lawPositionService.list(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4).isNotNull(LawPosition::getVideoId).ne(LawPosition::getVideoId, "").last(" limit 8"));
            if (CollectionUtil.isNotEmpty(list)) {
                for (LawPosition lawPosition : list) {
                    if (ObjectUtil.isNotEmpty(lawPosition.getVideoId())) {
                        List<SysFileInfo> videoList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getVideoId().split(SymbolConstant.COMMA))));
                        lawPosition.setVideoList(videoList);
                    }
                }
            }
            return list;
        }
        if (CommonConstant.CITY_LIST.contains(areaName)) {
            List<LawPosition> list = lawPositionService.list(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4).isNotNull(LawPosition::getVideoId).ne(LawPosition::getVideoId, "").eq(LawPosition::getCity, areaName));
            if (CollectionUtil.isNotEmpty(list)) {
                for (LawPosition lawPosition : list) {
                    if (ObjectUtil.isNotEmpty(lawPosition.getVideoId())) {
                        List<SysFileInfo> videoList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getVideoId().split(SymbolConstant.COMMA))));
                        lawPosition.setVideoList(videoList);
                    }
                }
            }
            return list;
        }
        if (ObjectUtil.isNotEmpty(areaName)) {
            List<LawPosition> list = lawPositionService.list(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4).isNotNull(LawPosition::getVideoId).ne(LawPosition::getVideoId, "").eq(LawPosition::getArea, areaName));
            if (CollectionUtil.isNotEmpty(list)) {
                for (LawPosition lawPosition : list) {
                    if (ObjectUtil.isNotEmpty(lawPosition.getVideoId())) {
                        List<SysFileInfo> videoList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().in(SysFileInfo::getId, Arrays.asList(lawPosition.getVideoId().split(SymbolConstant.COMMA))));
                        lawPosition.setVideoList(videoList);
                    }
                }
            }
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public void lawThemeMonthData() {
        //找还没有关联标签的数据
        List<String> ids = leResourcesService.selectHasLabel();
        if (CollectionUtil.isNotEmpty(ids)) {
            for (String id : ids) {
                //随机给主题宣传月的标签
                lerLabelMapper.insertLabelContact(IdUtil.fastSimpleUUID(), id, "1924357684396589057", "1924357513822633985");


            }
        }
    }

    @Override
    public List<JSONObject> lawActivityTheme() {
        List<JSONObject> resultList = bigScreenMapper.lawActivityTheme();

        // 翻译主题分类代码为显示名称
        for (JSONObject item : resultList) {
            String themeCode = item.getString("theme_code");
            if (StringUtils.isNotBlank(themeCode) && !"其他".equals(themeCode)) {
                // 如果是逗号分隔的多个主题，取第一个进行翻译
                String[] themeCodes = themeCode.split(",");
                String firstThemeCode = themeCodes[0].trim();
                String themeName = getDictDisplayName("pfzy_ztfl", firstThemeCode);
                if (StringUtils.isNotBlank(themeName)) {
                    item.put("name", themeName);
                } else {
                    item.put("name", firstThemeCode);
                }
            } else {
                item.put("name", "其他");
            }
            item.put("value", item.getInteger("count"));
        }

        return resultList;
    }

    @Override
    public List<JSONObject> lawActivityTrend() {
        List<JSONObject> resultList = bigScreenMapper.lawActivityTrendOrg();

        // 确保返回完整的12个月数据，没有数据的月份补0
        // List<JSONObject> completeList = new ArrayList<>();
        // Calendar cal = Calendar.getInstance();

        // for (int i = 11; i >= 0; i--) {
        //     cal.setTime(new Date());
        //     cal.add(Calendar.MONTH, -i);
        //     String month = new SimpleDateFormat("yyyy-MM").format(cal.getTime());

        //     JSONObject monthData = new JSONObject();
        //     monthData.put("month", month);
        //     monthData.put("count", 0);

        //     // 查找是否有该月的数据
        //     for (JSONObject item : resultList) {
        //         if (month.equals(item.getString("month"))) {
        //             monthData.put("count", item.getInteger("count"));
        //             break;
        //         }
        //     }

        //     completeList.add(monthData);
        // }

        return resultList;
    }

    @Override
    public List<JSONObject> lawActivityStyle() {
        List<JSONObject> resultList = bigScreenMapper.lawActivityStyle();

        // 处理封面图片URL和内容摘要
        for (JSONObject item : resultList) {
            //处理附件
            List<SysFileInfo> infoList = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda().eq(SysFileInfo::getReferId, item.getString("id")));
            if (CollectionUtil.isNotEmpty(infoList)) {
                item.put("fileList", infoList);
            }
        }

        return resultList;
    }

    @Override
    public Map<String, Object> lawActivityList() {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawActivityList(page);
        List<JSONObject> records = page.getRecords();

        // 处理返回数据
        for (JSONObject item : records) {
            // 翻译主题分类
            String themeType = item.getString("theme_type");
            if (StringUtils.isNotBlank(themeType)) {
                String themeName = getDictDisplayName("pfzy_ztfl", themeType);
                item.put("theme_name", StringUtils.isNotBlank(themeName) ? themeName : themeType);
            }

            // 翻译面向人群
            String audienceType = item.getString("audience_type");
            if (StringUtils.isNotBlank(audienceType)) {
                String audienceName = getDictDisplayName("pfzy-mxrq", audienceType);
                item.put("audience_name", StringUtils.isNotBlank(audienceName) ? audienceName : audienceType);
            }

            // 处理内容摘要
            String context = item.getString("context_");
            if (StringUtils.isNotBlank(context)) {
                String summary = context.length() > 200 ? context.substring(0, 200) + "..." : context;
                item.put("summary", summary);
            }
        }

        // 构造返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("rows", records);
        result.put("total", page.getTotal());
        result.put("pageNo", page.getCurrent());
        result.put("pageSize", page.getSize());

        return result;
    }

    @Override
    public List<JSONObject> lawThemeMonthVideo(String areaName) {
        // 查询主题宣传月标签
        LerLabelParam labelParam = new LerLabelParam();
        labelParam.setPid("1"); // 主题类标签的父ID
        labelParam.setStatus(1); // 已审核通过的标签
        List<LerLabel> themeLabels = lerLabelService.list(labelParam);

        // 查找"主题宣传月"标签
        String themeMonthLabelId = null;
        for (LerLabel label : themeLabels) {
            if (label.getName().contains("主题宣传月")) {
                themeMonthLabelId = label.getId();
                break;
            }
        }

        if (themeMonthLabelId == null) {
            return new ArrayList<>(); // 如果没有找到主题宣传月标签，返回空列表
        }
        // 查询视频
        List<JSONObject> videoList = bigScreenMapper.lawThemeMonthVideo(themeMonthLabelId, areaName);
        if (CollectionUtils.isNotEmpty(videoList)) {
            for (JSONObject jsonObject : videoList) {
                String filePath = jsonObject.getString("file_path");
                if (StringUtils.isNotBlank(filePath)) {
                    String generated = OssSignedUrlUtil.generatePresignedUrlWithPublicDomain(filePath);
                    jsonObject.put("file_path", generated);
                    jsonObject.put("out_path", generated);
                }
            }
            return videoList;
        }
        return Collections.emptyList();
    }
}
