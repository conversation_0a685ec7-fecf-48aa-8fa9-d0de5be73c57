<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.modular.statistic.mapper.BigScreenMapper">


    <select id="lawCultivateArea" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT
            SUM(amount) as value,
            #{level} as code
        FROM
            (SELECT COUNT(*) AS amount, org_id FROM sys_emp GROUP BY org_id) a
        WHERE
            a.org_id IN (SELECT id FROM sys_org WHERE `level` = #{level} AND `name` LIKE '%普法%')
    </select>
    <select id="lawCultivateSex" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT
            COUNT(*) AS value,
        sex AS code
        FROM
            sys_user
        WHERE
            id IN (
            SELECT job_num
            FROM sys_emp
            WHERE org_id IN (
            SELECT id FROM sys_org
            WHERE `name` LIKE '%普法%'
            )
            )
          AND sex IN (1, 2)
         and status=0
        <if test="areaCode != null">
            and full_area_id LIKE concat('%',#{areaCode},'%')
        </if>
        GROUP BY
            sex
        order by sex asc
    </select>
    <select id="lawLearnerPolitical" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT
            COUNT(*) AS VALUE,
            zzmm AS name
        FROM
            law_understand_people
        WHERE
            1=1
        <if test="areaCode != null">
            and address_code LIKE concat('%',#{areaCode},'%')
        </if>
        GROUP BY
            zzmm
    </select>
    <select id="lawLearnerEducation" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT
            COUNT( * ) AS VALUE,
	        education AS code
        FROM
            law_understand_people
        WHERE
            1=1
        <if test="areaCode != null">
        and address_code LIKE concat('%',#{areaCode},'%')
        </if>
        GROUP BY
            education
    </select>
    <select id="lawLearnerPersonType" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT 1 AS code, COUNT(*) AS value FROM law_understand_people WHERE FIND_IN_SET('csgb', person_type)
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
        UNION ALL
        SELECT 2 AS code, COUNT(*) AS value FROM law_understand_people WHERE FIND_IN_SET('sqgzz', person_type)
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
        UNION ALL
        SELECT 3 AS code, COUNT(*) AS value FROM law_understand_people WHERE FIND_IN_SET('rmtjy', person_type)
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
        UNION ALL
        SELECT 4 AS code, COUNT(*) AS value FROM law_understand_people WHERE FIND_IN_SET('wgy', person_type)
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
        UNION ALL
        SELECT 5 AS code, COUNT(*) AS value FROM law_understand_people WHERE FIND_IN_SET('wlry', person_type)
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
        UNION ALL
        SELECT 6 AS code, COUNT(*) AS value FROM law_understand_people WHERE FIND_IN_SET('ldbywy', person_type)
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
        UNION ALL
        SELECT 7 AS code, COUNT(*) AS value FROM law_understand_people WHERE FIND_IN_SET('qt', person_type)
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
    </select>
    <select id="lawCultivateUser" resultType="com.concise.modular.statistic.vo.LawUserVo">
        SELECT
            b.*,
            sys_org.org_name AS p_org_name,
            sys_org.LEVEL AS LEVEL
        FROM
            (
                SELECT
                    a.*,
                    sys_org.pid
                FROM
                    (
                        SELECT
                            sys_user.id AS id,
                            sys_user.NAME AS NAME,
                            sys_user.phone AS phone,
                            sys_emp.org_id AS org_id,
                            sys_emp.org_name AS org_name,
                            sys_user.sorts AS sorts
                        FROM
                            sys_emp
                                LEFT JOIN sys_user ON sys_emp.job_num = sys_user.id
                        WHERE
                            sys_user.status=0 and
                            sys_emp.org_name LIKE '%普法%'
                        ORDER BY
                            sorts ASC
                    ) a
                        LEFT JOIN sys_org ON a.org_id = sys_org.id
                WHERE
                    sys_org.`level` = #{code}
                  <if test="orgIds !=null and orgIds.size() > 0">
                      and sys_org.id in
                      <foreach collection="orgIds" item="id" open="(" separator="," close=")">
                          #{id}
                      </foreach>
                  </if>
            ) b
                LEFT JOIN sys_org ON b.pid = sys_org.id
        ORDER BY
            b.org_id asc,
            b.sorts ASC
    </select>
    <select id="lawResponsibility" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT COUNT(*) AS VALUE,
            org_type AS CODE,
        CASE
                WHEN org_type = 1 THEN
                '省守法普法协调小组成员单位'
                WHEN org_type = 2 THEN
                '省级行政执法单位'
                WHEN org_type = 3 THEN
                '其他省级和省部属普法责任单位'
                END
                AS NAME
        FROM
            res_org_manage
        GROUP BY
            org_type
    </select>
    <select id="threeDimensional" resultType="com.concise.modular.evaluation.examresult.entity.ExamResult">
        SELECT
            AVG( first_identity ) AS first_identity,
            AVG( first_awareness ) AS first_awareness,
            AVG( first_participation ) AS first_participation
        FROM
            exam_result
        WHERE 1=1
        <if test="areaName!=null and areaName !=''">
            and city=#{areaName}
        </if>
    </select>
    <select id="fiveLaw" resultType="com.concise.modular.evaluation.examresult.entity.ExamResult">
        SELECT
            AVG( second_use_law ) AS second_use_law,
            AVG( second_study_law ) AS second_study_law,
            AVG( second_abiding_law ) AS second_abiding_law,
            AVG(second_respect_law) AS second_respect_law
        FROM
            exam_result
        WHERE 1=1
        <if test="areaName!=null and areaName !=''">
            and city=#{areaName}
        </if>
    </select>
    <select id="lawFunds" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT
            SUM( funding ) AS score,
            city AS name

        FROM
            res_fund_manage
        WHERE
            `year` = #{year}
        GROUP BY
            city
    </select>
    <select id="lawFundsArea" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT
            funding AS score,
            area AS name
        FROM
            res_fund_manage
        WHERE
            `year` = #{year}
          AND city = #{areaName}
    </select>
    <select id="lawResource" resultType="com.concise.modular.statistic.vo.ResourceVo">
        SELECT LEFT
            ( type, 1 ) AS code,
            COUNT( * ) AS value
        FROM
            law_education_resources
        where status_ in (1,2,5,6)
        <if test="deptIds !=null and deptIds.size() > 0">
         and dept_id in
            <foreach collection="deptIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
            LEFT ( type, 1 );
    </select>
    <select id="lawResourceDetail" resultType="com.concise.modular.statistic.vo.ResourceVo">
        SELECT type AS code,
        COUNT( * ) AS value
        FROM
        law_education_resources
        where 1=1
        <if test="deptIds !=null and deptIds.size() > 0">
            and dept_id in
            <foreach collection="deptIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
        type;
    </select>
    <select id="pointInfo" resultType="com.alibaba.fastjson.JSONObject">
        (SELECT id ,position_name AS `name`,latitude,longitude,position_type AS type FROM law_position WHERE
        position_type IN (3,4,6)
        <if test="name!=null and name !=''">
            and position_name like concat('%', #{name}, '%')
        </if>
        <if test="deptIds !=null and deptIds.size() > 0">
            and org_id in
            <foreach collection="deptIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>)
        UNION ALL
        (SELECT id ,point_name AS `name` ,latitude,longitude ,5 AS type FROM observation_point_info WHERE status_=4
        <if test="name!=null and name !=''">
            and point_name like concat('%', #{name}, '%')
        </if>
        <if test="areaCode !=null and areaCode !=''">
            and address_id like concat('%', #{areaCode}, '%')
        </if>
        )
    </select>
    <select id="pointCount" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT
            city AS NAME,
            COUNT(*) AS VALUE
        FROM
            observation_point_info
        WHERE
            point_level = 1
        GROUP BY
            city
    </select>
    <select id="lawPositionDetail" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,
            city,
            position_name AS name,
            latitude,
            longitude
        FROM
            law_position
        WHERE
            position_type =3
        <if test="city!=null and city !=''">
            and city=#{city}
        </if>
    </select>
    <select id="lawPositionDetailNum" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            city,
            COUNT( * ) AS num

        FROM
            law_position
        WHERE
            position_type = #{type}
        GROUP BY
            city
    </select>
    <select id="obPointCount" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            city,
            COUNT( * ) AS num
        FROM
            observation_point_info
        WHERE
            status_ = 4
          and point_level = 1
        and city is not null
        GROUP BY
            city
    </select>
    <select id="lawPositionDetailNumArea" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            COUNT( * ) AS num,
	        area AS city
        FROM
            law_position
        WHERE
            city = #{areaName}
        and position_type = #{type}
        GROUP BY
            area
    </select>
    <select id="obPointCountArea" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            area AS city,
            COUNT( * ) AS num
        FROM
            observation_point_info
        WHERE
            status_ = 4
          and point_level = 1
          AND city=#{areaName}
        GROUP BY
            area
    </select>
    <select id="pointInfoMetrics" resultType="com.alibaba.fastjson.JSONObject">
        (SELECT id ,position_name AS `name`,position_type AS type,address_detail FROM law_position WHERE
        position_type IN (3,4)
        <if test="name!=null and name !=''">
            and position_name like concat('%', #{name}, '%')
        </if>)
        UNION ALL
        (SELECT id ,point_name AS `name`,5 AS type,address_detail FROM observation_point_info WHERE status_=4
        <if test="name!=null and name !=''">
            and point_name=#{name}
        </if>)
    </select>

    <select id="lawEducationCityDistribution" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            city_area_info.area as city,
            SUM(CASE WHEN benchmark_manage.version_type = 1 THEN 1 ELSE 0 END) as tsb,
            SUM(CASE WHEN benchmark_manage.version_type = 2 THEN 1 ELSE 0 END) as dfb,
            SUM(CASE WHEN benchmark_manage.version_type = 3 THEN 1 ELSE 0 END) as hyb
        FROM
            city_area_info
            LEFT JOIN benchmark_manage
                ON benchmark_manage.city = city_area_info.area
                AND benchmark_manage.version_type IN (2, 3)
        WHERE
            city_area_info.pid = '330000'
        GROUP BY
            city_area_info.area
        ORDER BY
            city_area_info.area
    </select>


    <select id="lawEducationCityDistributionKeyword" resultType="java.lang.String">
        SELECT
            keyword
        FROM
            benchmark_manage
        where
        keyword is not null
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            limit 8
    </select>


    <select id="lawEducationVersionCount" resultType="com.alibaba.fastjson.JSONObject">
        SELECT t.version_type, COALESCE(b.num, 0) as num
        FROM (
            SELECT 'tsb' as version_type UNION ALL
            SELECT 'dfb' UNION ALL
            SELECT 'hyb'
        ) t
        LEFT JOIN (
            SELECT
                CASE
                    WHEN version_type = 1 THEN 'tsb'
                    WHEN version_type = 2 THEN 'dfb'
                    WHEN version_type = 3 THEN 'hyb'
                END AS version_type,
                COUNT(*) AS num
            FROM benchmark_manage
            WHERE version_type IS NOT NULL
            GROUP BY version_type
        ) b ON t.version_type = b.version_type
    </select>
    <select id="lawCultivateAreaByOrgId" resultType="com.concise.modular.statistic.vo.ScreenVo">
        SELECT
            SUM( amount ) as value,
                #{level} as code
        FROM
            ( SELECT COUNT( * ) AS amount, org_id FROM sys_emp GROUP BY org_id ) a
        WHERE
            a.org_id IN ( SELECT id FROM sys_org WHERE `level` = #{level} AND `name` LIKE '%普法%' AND pids like concat('%',#{orgId},'%') )
    </select>
    <select id="lawPositionDetailArea" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        id,
        area as city,
        position_name AS name,
        latitude,
        longitude
        FROM
        law_position
        WHERE
        position_type =3
        <if test="areaName!=null and areaName !=''">
            and area=#{areaName}
        </if>
    </select>
    <select id="obPointCountAreaQx" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,
            area AS city,
            point_name AS name,
            latitude,
            longitude
        FROM
            observation_point_info
        WHERE
            status_ = 4
          and point_level = 1
          AND area=#{areaName}

    </select>
    <select id="lawPositionDetailNumAreaQx" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,
            area as city,
            position_name AS name,
            latitude,
            longitude
        FROM
            law_position
        WHERE
            position_type =#{type} and
            area = #{areaName}
    </select>
    <select id="lawResourceFourType" resultType="com.concise.modular.statistic.vo.ResourceVo">
        SELECT
            COUNT(CASE WHEN status_ = 6 THEN 1 END) AS ycy,
            COUNT(CASE WHEN status_ != 6 THEN 1 END) AS wcy,
            COUNT(CASE WHEN dept_id LIKE '%DUTY_%' THEN 1 END) AS pfzrz,
            COUNT(CASE WHEN dept_id NOT LIKE '%DUTY_%' THEN 1 END) AS sfxz
        FROM
            law_education_resources
        WHERE
            type !=0
        and status_ in (1,2,5,6)
        <if test="deptIds !=null and deptIds.size() > 0">
            and dept_id in
            <foreach collection="deptIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>


    <select id="lawLearnerTotal" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM law_understand_people WHERE address_code LIKE CONCAT('%', #{areaCode}, '%')
    </select>
    <select id="lawEducationCityDistributionCity" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            city_area_info.area AS city,
            SUM(CASE WHEN benchmark_manage.version_type = 2 THEN 1 ELSE 0 END) AS dfb,
            SUM(CASE WHEN benchmark_manage.version_type = 3 THEN 1 ELSE 0 END) AS hyb
        FROM
            city_area_info
                LEFT JOIN benchmark_manage ON benchmark_manage.area = city_area_info.area
                AND benchmark_manage.version_type IN (2, 3)
        WHERE
            city_area_info.pid = #{areaCode}
        GROUP BY
            city_area_info.area
        ORDER BY
            city_area_info.area
    </select>
    <select id="lawEducationCityDistributionQx" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            city_area_info.area AS city,
            SUM(CASE WHEN benchmark_manage.version_type = 2 THEN 1 ELSE 0 END) AS dfb,
            SUM(CASE WHEN benchmark_manage.version_type = 3 THEN 1 ELSE 0 END) AS hyb
        FROM
            city_area_info
                LEFT JOIN benchmark_manage ON benchmark_manage.area = city_area_info.area
                AND benchmark_manage.version_type IN (2, 3)
        WHERE
            city_area_info.id = #{areaCode}
        GROUP BY
            city_area_info.area
        ORDER BY
            city_area_info.area
    </select>
    <select id="lawEducationCityDistributionKeywordQx" resultType="java.lang.String">
        SELECT
        keyword
        FROM
        benchmark_manage
        where
        keyword is not null
        <if test="city != null and city != ''">
            and area = #{city}
        </if>
        limit 8
    </select>
    <select id="villageList" resultType="com.concise.modular.law.lawposition.entity.LawPosition">
        SELECT
            id,position_name,position_type,org_id,org_name,latitude,longitude
        FROM
            law_position
        WHERE
            position_type = '4'
            <if test="areaCode!=null and areaCode !=''">
                and address_code like concat('%', #{areaCode}, '%')
            </if>
    </select>
    <select id="lawReport" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            t.city,
            t.count
        FROM
            (
                SELECT
                    city,
                    COUNT(*) AS count
                FROM
                    law_education_resources
                        LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
                WHERE
                    city IS NOT NULL
                    and law_education_resources.source_='0'
                    AND law_education_resources.status_ in (1, 2, 5, 6)
                GROUP BY
                    city
            ) t
                LEFT JOIN city_area_info c ON t.city = c.area
        ORDER BY
            c.id
    </select>
    <select id="lawReportCity" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            sys_org.area,
            COUNT(*) AS count
        FROM
            law_education_resources
            LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
        WHERE
            sys_org.city = #{areaName}
          AND area IS NOT NULL
          and law_education_resources.source_='0'
          AND law_education_resources.status_ in (1, 2, 5, 6)
        GROUP BY
            sys_org.area
    </select>
    <select id="lawReportQx" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            sys_org.area,
            COUNT(*) AS count
        FROM
            law_education_resources
            LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
        WHERE
            sys_org.area = #{areaName}
          AND area IS NOT NULL
          and law_education_resources.source_='0'
          AND law_education_resources.status_ in (1, 2, 5, 6)
        GROUP BY
            sys_org.area
    </select>
    <select id="villageTotal" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            law_position
        WHERE
            law_position.position_type = '4' and
            address_code LIKE concat('%', #{areaCode}, '%')
    </select>
    <select id="villageTotalCity" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            area AS name,
            COUNT(*) AS value
        FROM
            law_position
        WHERE
            law_position.position_type = '4' and
            address_code LIKE concat('%', #{areaCode}, '%')
        GROUP BY
            area
    </select>

    <!-- 获取测评参与人数 -->
    <select id="examTestCount" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM
            exam_result er
        WHERE
            er.status = '1'
        <if test="areaCode != null and areaCode != ''">
            AND er.area_code LIKE CONCAT('%', #{areaCode}, '%')
        </if>
    </select>

    <!-- 获取测评参与人数（使用区域代码列表） -->
    <select id="examTestCountByAreaCodes" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM
            exam_result er
        WHERE
            er.status = '1'
        <if test="areaCodes != null and areaCodes.size() > 0">
            AND er.area_code IN
            <foreach collection="areaCodes" item="areaCode" open="(" separator="," close=")">
                #{areaCode}
            </foreach>
        </if>
    </select>

    <!-- 获取测评参与人数（使用区域名称列表） -->
    <select id="examTestCountByAreaNames" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM
            exam_result er
        WHERE
            er.status = '1'
        <if test="areaNames != null and areaNames.size() > 0">
            AND (er.city IN
            <foreach collection="areaNames" item="areaName" open="(" separator="," close=")">
                #{areaName}
            </foreach>
            OR er.area IN
            <foreach collection="areaNames" item="areaName" open="(" separator="," close=")">
                #{areaName}
            </foreach>)
        </if>
    </select>

    <!-- 获取基准点自测参与人数 -->
    <select id="benchmarkTestCount" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM
            benchmark_test_result btr
        WHERE
            btr.status = '1'
        <if test="areaCode != null and areaCode != ''">
            AND btr.area_code LIKE CONCAT('%', #{areaCode}, '%')
        </if>
    </select>

    <!-- 获取基准点自测参与人数（使用区域代码列表） -->
    <select id="benchmarkTestCountByAreaCodes" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM
            benchmark_test_result btr
        WHERE
            btr.status = '1'
        <if test="areaCodes != null and areaCodes.size() > 0">
            AND btr.area_code IN
            <foreach collection="areaCodes" item="areaCode" open="(" separator="," close=")">
                #{areaCode}
            </foreach>
        </if>
    </select>

    <!-- 分页查询测评详细数据（使用区域代码列表） -->
    <select id="lawTestPageByAreaCodes" resultType="com.alibaba.fastjson.JSONObject">
        <choose>
            <when test="type == 1">
                SELECT
                    er.id,
                    er.user_name,
                    er.address,
                    er.area,
                    er.total_score as score,
                    er.create_time,
                    er.status
                FROM exam_result er
                WHERE er.status = '1'
                <if test="areaNames != null and areaNames.size() > 0">
                    AND (er.city IN
                    <foreach collection="areaNames" item="areaName" open="(" separator="," close=")">
                        #{areaName}
                    </foreach>
                    OR er.area IN
                    <foreach collection="areaNames" item="areaName" open="(" separator="," close=")">
                        #{areaName}
                    </foreach>)
                </if>
            </when>
            <otherwise>
                SELECT
                    er.id,
                    er.user_name,
                    er.org_name as address,
                    er.org_name as area,
                    CONCAT(er.right_num, '/', er.total_num) as score,
                    er.create_time,
                    er.status
                FROM benchmark_test_result er
                WHERE er.status = '1'
                <if test="areaCodes != null and areaCodes.size() > 0">
                    AND er.area_code IN
                    <foreach collection="areaCodes" item="areaCode" open="(" separator="," close=")">
                        #{areaCode}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询测评详细数据 -->
    <select id="lawTestPage" resultType="com.alibaba.fastjson.JSONObject">
        <choose>
            <when test="type == 1">
                SELECT
                    er.id,
                    er.user_name,
                    er.address,
                    er.area,
                    er.total_score as score,
                    er.create_time,
                    er.status
                FROM exam_result er
                WHERE er.status = '1'
                <if test="areaCode != null and areaCode != ''">
                    AND er.area_code LIKE CONCAT('%', #{areaCode}, '%')
                </if>
            </when>
            <otherwise>
                SELECT
                    er.id,
                    er.user_name,
                    er.user_id as phone,
                    er.org_name as address,
                    er.org_name as area,
                    CONCAT(er.right_num, '/', er.total_num) as score,
                    er.create_time,
                    er.status
                FROM benchmark_test_result er
                WHERE er.status = '1'
                <if test="areaCode != null and areaCode != ''">
                    AND er.area_code LIKE CONCAT('%', #{areaCode}, '%')
                </if>
            </otherwise>
        </choose>
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询测评详细数据（使用区域代码列表，带分页） -->
    <select id="lawTestPageWithPaginationByAreaCodes" resultType="com.alibaba.fastjson.JSONObject">
        <choose>
            <when test="type == 2">
                SELECT
                    er.id,
                    er.user_name,
                    er.address,
                    er.target_audience,
                    er.exam_paper as paper_name,
                    er.create_time
                FROM exam_result er
                WHERE er.status = '1'
                <if test="areaNames != null and areaNames.size() > 0">
                    AND (er.city IN
                    <foreach collection="areaNames" item="areaName" open="(" separator="," close=")">
                        #{areaName}
                    </foreach>
                    OR er.area IN
                    <foreach collection="areaNames" item="areaName" open="(" separator="," close=")">
                        #{areaName}
                    </foreach>)
                </if>
            </when>
            <otherwise>
                SELECT
                    er.id,
                    er.user_name,
                    er.target_audience,
                    er.org_name as address,
                    er.title as paper_name,
                    er.area_code as address,
                    er.create_time
                FROM benchmark_test_result er
                WHERE er.status = '1'
                <if test="areaCodes != null and areaCodes.size() > 0">
                    AND er.area_code IN
                    <foreach collection="areaCodes" item="areaCode" open="(" separator="," close=")">
                        #{areaCode}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询测评详细数据（带分页） -->
    <select id="lawTestPageWithPagination" resultType="com.alibaba.fastjson.JSONObject">
        <choose>
            <when test="type == 2">
                SELECT
                    er.id,
                    er.user_name,
                    er.address,
                    er.target_audience,
                    er.exam_paper as paper_name,
                    er.create_time
                FROM exam_result er
                WHERE er.status = '1'
                <if test="areaCode != null and areaCode != ''">
                    AND er.area_code LIKE CONCAT('%', #{areaCode}, '%')
                </if>
            </when>
            <otherwise>
                SELECT
                    er.id,
                    er.user_name,
                    er.target_audience,
                    er.org_name as address,
                    er.title as paper_name,
                    er.area_code as address,
                    er.create_time
                FROM benchmark_test_result er
                WHERE er.status = '1'
                <if test="areaCode != null and areaCode != ''">
                    AND er.area_code LIKE CONCAT('%', #{areaCode}, '%')
                </if>
            </otherwise>
        </choose>
        ORDER BY create_time DESC
    </select>

    <select id="getCityActivityStatistics" resultType="java.util.Map">
        SELECT
            obi.city as city_name,
            COUNT(am.id) as activity_count
        FROM
            activity_manage am
        LEFT JOIN
            observation_point_info obi ON am.point_id = obi.id
        WHERE
            1=1
            <if test="areaCode != null and areaCode != ''">
            AND obi.address_code LIKE concat('%', #{areaCode}, '%')
            </if>
        GROUP BY
            obi.city
        ORDER BY
            activity_count DESC
    </select>
    <select id="lawCultivateAnalyseProvince" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            COUNT(*) AS amount,
            sys_org.city
        FROM
            sys_emp
                LEFT JOIN sys_org ON sys_emp.org_id = sys_org.id
        WHERE
            sys_org.org_name LIKE '%普法%'
          AND sys_org.city IS NOT NULL
        GROUP BY
            city
    </select>
    <select id="lawCultivateAnalyseCity" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            COUNT(*) AS amount,
            sys_org.area as city
        FROM
            sys_emp
                LEFT JOIN sys_org ON sys_emp.org_id = sys_org.id
        WHERE
            sys_org.org_name LIKE '%普法%'
          AND sys_org.city = #{areaName}
          AND sys_org.area IS NOT NULL
        GROUP BY
            area
    </select>
    <select id="lawCultivateAnalyseArea" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            COUNT(*) AS amount,
            sys_org.area as city
        FROM
            sys_emp
                LEFT JOIN sys_org ON sys_emp.org_id = sys_org.id
        WHERE
            sys_org.org_name LIKE '%普法%'
          AND sys_org.area = #{areaName}
          AND sys_org.area IS NOT NULL
        GROUP BY
            area
    </select>
    <select id="lawUnderstandProvince" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            city_name AS city,
            COUNT(*) AS amount
        FROM
            law_understand_people
        WHERE
            city_name IS NOT NULL
        GROUP BY
            city_name
    </select>
    <select id="lawUnderstandCity" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            area_name AS city,
            COUNT(*) AS amount
        FROM
            law_understand_people
        WHERE
            area_name IS NOT NULL
          AND city_name = #{areaName}
        GROUP BY
            area_name
    </select>
    <select id="lawUnderstandArea" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            area_name AS city,
            COUNT(*) AS amount
        FROM
            law_understand_people
        WHERE
            area_name IS NOT NULL
          AND area_name = #{areaName}
        GROUP BY
            area_name
    </select>

    <select id="lawCultivateUserList" resultType="com.concise.modular.statistic.vo.LawUserVo">
        SELECT
            u.id,
            u.account,
            u.name,
            u.sex,
            u.phone,
            u.email,
            o.name as orgName
        FROM
            sys_user u
        LEFT JOIN sys_emp e ON u.id = e.job_num
        LEFT JOIN sys_org o ON e.org_id = o.id
        WHERE
            e.org_id IN (
                SELECT id FROM sys_org
                WHERE `name` LIKE '%普法%'
            )
            AND u.status = 0
        <if test="areaCode != null">
            AND o.full_area_id LIKE concat('%',#{areaCode},'%')
        </if>
        ORDER BY u.id
    </select>

    <select id="lawLearnerUserList" resultType="com.concise.modular.statistic.vo.LawUserVo">
        SELECT
            id,
            name,
            sex,
            telephone as phone,
            address_name as orgName
        FROM
            law_understand_people
        WHERE
            1=1
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
        ORDER BY id
    </select>

    <!-- 获取普法干部用户列表（带分页） -->
    <select id="lawCultivateUserPageWithPagination" resultType="com.concise.modular.statistic.vo.LawUserVo">
        SELECT
            u.id,
            u.account,
            u.name,
            u.sex,
            u.phone,
            u.email,
            o.name as orgName
        FROM
            sys_user u
        LEFT JOIN sys_emp e ON u.id = e.job_num
        LEFT JOIN sys_org o ON e.org_id = o.id
        WHERE
            e.org_id IN (
                SELECT id FROM sys_org
                WHERE `name` LIKE '%普法%'
            )
            AND u.status = 0
        <if test="areaCode != null">
            AND o.full_area_id LIKE concat('%',#{areaCode},'%')
        </if>
        ORDER BY u.id
    </select>

    <!-- 获取法律明白人用户列表（带分页） -->
    <select id="lawLearnerUserPageWithPagination" resultType="com.concise.modular.statistic.vo.LawUserVo">
        SELECT
            id,
            name,
            sex,
            telephone as phone,
            address_name as orgName
        FROM
            law_understand_people
        WHERE
            1=1
        <if test="areaCode != null">
            AND address_code LIKE concat('%',#{areaCode},'%')
        </if>
        ORDER BY id
    </select>
    <!-- 民主法治示范村分页查询 -->
    <select id="lawVillageTotalPage" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            lp.id,
            lp.position_name,
            lp.position_level,
            lp.city,
            lp.area,
            lp.address_detail,
            CASE
                WHEN lp.position_level = 1 THEN '国家级'
                WHEN lp.position_level = 2 THEN '省级'
                ELSE '其他'
            END as level_name
        FROM
            law_position lp
        WHERE
            lp.position_type = '4'
            <choose>
                <when test="areaName == '浙江省'">
                    AND lp.city IS NOT NULL
                </when>
                <when test="areaName != null and areaName.endsWith('市')">
                    AND lp.city = #{areaName}
                </when>
                <when test="areaName != null">
                    AND lp.area = #{areaName}
                </when>
            </choose>
            <if test="level != null">
                AND lp.position_level = #{level}
            </if>
        ORDER BY
            lp.create_time DESC
    </select>

    <!-- 民主法治示范村分页查询（带分页） -->
    <select id="lawVillageTotalPageWithPagination" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            lp.id,
            lp.position_name,
            lp.position_level,
            lp.city,
            lp.area,
            lp.address_detail,
            CASE
                WHEN lp.position_level = 1 THEN '国家级'
                WHEN lp.position_level = 2 THEN '省级'
                ELSE '其他'
            END as level_name
        FROM
            law_position lp
        WHERE
            lp.position_type = '4'
            <choose>
                <when test="areaName == '浙江省'">
                    AND lp.city IS NOT NULL
                </when>
                <when test="areaName != null and areaName.endsWith('市')">
                    AND lp.city = #{areaName}
                </when>
                <when test="areaName != null">
                    AND lp.area = #{areaName}
                </when>
            </choose>
            <if test="level != null">
                AND lp.position_level = #{level}
            </if>
        ORDER BY
            lp.create_time DESC
    </select>

    <!-- 分页查询法治文化阵地数据 -->
    <select id="lawCulturePage" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            lp.id,
            lp.position_name,
            lp.position_type,
            lp.position_level,
            lp.address_name,
            lp.city,
            lp.area,
            lp.address_detail,
            lp.latitude,
            lp.longitude,
            lp.create_time
        FROM
            law_position lp
        WHERE
            lp.position_type IN (7, 8, 9, 10, 11, 12)
            <if test="type != null">
                AND lp.position_type = #{type}
            </if>
            <if test="positionLevel != null">
                AND lp.position_level = #{positionLevel}
            </if>
            <if test="areaName != null and areaName != '浙江省'">
                <choose>
                    <when test="areaName.endsWith('市')">
                        AND lp.city = #{areaName}
                    </when>
                    <otherwise>
                        AND lp.area = #{areaName}
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            lp.create_time DESC
    </select>

    <!-- 分页查询法治文化阵地数据（带分页） -->
    <select id="lawCulturePageWithPagination" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            lp.id,
            lp.position_name,
            lp.position_type,
            lp.position_level,
            lp.address_name,
            lp.city,
            lp.area,
            lp.address_detail,
            lp.latitude,
            lp.longitude,
            lp.create_time
        FROM
            law_position lp
        WHERE
            lp.position_type IN (7, 8, 9, 10, 11, 12)
            <if test="type != null">
                AND lp.position_type = #{type}
            </if>
            <if test="positionLevel != null">
                AND lp.position_level = #{positionLevel}
            </if>
            <if test="areaName != null and areaName != '浙江省'">
                <choose>
                    <when test="areaName.endsWith('市')">
                        AND lp.city = #{areaName}
                    </when>
                    <otherwise>
                        AND lp.area = #{areaName}
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            lp.create_time DESC
    </select>

    <!-- 分页查询法治文艺资源数据 -->
    <select id="lawArtPage" resultType="com.alibaba.fastjson.JSONObject">
        SELECT SQL_CALC_FOUND_ROWS
            id,
            title,
            context_,
            dept_name,
            create_time as share_time,
            type,

            theme_type,
            audience_type,
            CASE
                WHEN type = '11' THEN '漫画'
                WHEN type = '12' THEN '宣传画'
                WHEN type = '13' THEN '海报'
                WHEN type = '31' THEN '动画'
                WHEN type = '32' THEN '宣传片'
                WHEN type = '33' THEN '微电影'
                WHEN type = '34' THEN '情景剧'
                WHEN type = '41' THEN '视频课件'
                WHEN type = '42' THEN '文档课件'
                WHEN type = '1' THEN '公益广告'
                ELSE '其他'
            END as type_name
        FROM
            law_education_resources
        WHERE
            type = #{type}
            AND status_ in (1, 2, 5, 6)
            <if test="areaName != null and areaName != '浙江省'">
                <choose>
                    <when test="areaName.endsWith('市')">
                        AND dept_name LIKE CONCAT('%', #{areaName}, '%')
                    </when>
                    <otherwise>
                        AND dept_name LIKE CONCAT('%', #{areaName}, '%')
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            create_time DESC
    </select>

    <!-- 分页查询法治文艺主题分类资源数据 -->
    <select id="lawArtCategoryPage" resultType="com.alibaba.fastjson.JSONObject">
        SELECT SQL_CALC_FOUND_ROWS
            id,
            title,
            context_,
            dept_name,
            create_time as share_time,
            type,

            theme_type,
            audience_type,
            CASE
                WHEN type = '11' THEN '漫画'
                WHEN type = '12' THEN '宣传画'
                WHEN type = '13' THEN '海报'
                WHEN type = '31' THEN '动画'
                WHEN type = '32' THEN '宣传片'
                WHEN type = '33' THEN '微电影'
                WHEN type = '34' THEN '情景剧'
                WHEN type = '41' THEN '视频课件'
                WHEN type = '42' THEN '文档课件'
                WHEN type = '1' THEN '公益广告'
                ELSE '其他'
            END as type_name
        FROM
            law_education_resources
        WHERE
            status_ in (1, 2, 5, 6)
            AND EXISTS (
                SELECT 1 FROM law_education_resources_label_contact lc
                LEFT JOIN law_education_resources_label l ON lc.label_id = l.id
                WHERE lc.res_id = law_education_resources.id
                AND l.name_ LIKE CONCAT('%', #{labelName}, '%')
                AND l.status_ = 1
            )
            <if test="areaName != null and areaName != '浙江省'">
                <choose>
                    <when test="areaName.endsWith('市')">
                        AND dept_name LIKE CONCAT('%', #{areaName}, '%')
                    </when>
                    <otherwise>
                        AND dept_name LIKE CONCAT('%', #{areaName}, '%')
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            create_time DESC
    </select>

    <!-- 按地区统计主题宣传月资源数量 -->
    <select id="lawThemeMonthStatistics" resultType="com.alibaba.fastjson.JSONObject">
        <choose>
            <when test="areaName == '浙江省'">
                <!-- 按地市统计 -->
                SELECT
                    sys_org.city,
                    COUNT(*) AS count
                FROM
                    law_education_resources
                    LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
                WHERE
                    law_education_resources.status_ in (1, 2, 5, 6)
                    AND law_education_resources.source_ = '0'
                    AND sys_org.city IS NOT NULL
                    AND EXISTS (
                        SELECT 1 FROM law_education_resources_label_contact lc
                        WHERE lc.res_id = law_education_resources.id
                        AND lc.label_pid = #{labelPid}
                    )
                GROUP BY
                    sys_org.city
                ORDER BY
                    count DESC
            </when>
            <when test="areaName != null and areaName.endsWith('市')">
                <!-- 按区县统计 -->
                SELECT
                    sys_org.area,
                    COUNT(*) AS count
                FROM
                    law_education_resources
                    LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
                WHERE
                    law_education_resources.status_ in (1, 2, 5, 6)
                    AND law_education_resources.source_ = '0'
                    AND sys_org.city = #{areaName}
                    AND sys_org.area IS NOT NULL
                    AND EXISTS (
                        SELECT 1 FROM law_education_resources_label_contact lc
                        WHERE lc.res_id = law_education_resources.id
                        AND lc.label_pid = #{labelPid}
                    )
                GROUP BY
                    sys_org.area
                ORDER BY
                    count DESC
            </when>
            <otherwise>
                <!-- 按单位统计 -->
                SELECT
                    law_education_resources.dept_name AS unit,
                    COUNT(*) AS count
                FROM
                    law_education_resources
                    LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
                WHERE
                    law_education_resources.status_ in (1, 2, 5, 6)
                    AND law_education_resources.source_ = '0'
                    AND EXISTS (
                        SELECT 1 FROM law_education_resources_label_contact lc
                        WHERE lc.res_id = law_education_resources.id
                        AND lc.label_pid = #{labelPid}
                    )
                    <if test="areaName != null and areaName != ''">
                        AND sys_org.area = #{areaName}
                    </if>
                GROUP BY
                    law_education_resources.dept_name
                ORDER BY
                    count DESC
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询主题宣传月资源数据 -->
    <select id="lawThemeMonthPage" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            law_education_resources.id,
            law_education_resources.title,
            law_education_resources.context_,
            law_education_resources.dept_name,
            law_education_resources.create_time,
            law_education_resources.type,
            law_education_resources.theme_type,
            law_education_resources.audience_type,
            sys_org.city,
            sys_org.area,
            CASE
                WHEN law_education_resources.type = '11' THEN '漫画'
                WHEN law_education_resources.type = '12' THEN '宣传画'
                WHEN law_education_resources.type = '13' THEN '海报'
                WHEN law_education_resources.type = '31' THEN '动画'
                WHEN law_education_resources.type = '32' THEN '宣传片'
                WHEN law_education_resources.type = '33' THEN '微电影'
                WHEN law_education_resources.type = '34' THEN '情景剧'
                WHEN law_education_resources.type = '41' THEN '视频课件'
                WHEN law_education_resources.type = '42' THEN '文档课件'
                WHEN law_education_resources.type = '1' THEN '公益广告'
                ELSE '其他'
            END as type_name
        FROM
            law_education_resources
            LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
        WHERE
            law_education_resources.status_ in (1, 2, 5, 6)
            AND law_education_resources.source_ = '1'
            AND EXISTS (
                SELECT 1 FROM law_education_resources_label_contact lc
                WHERE lc.res_id = law_education_resources.id
                AND lc.label_pid = #{labelPid}
            )
            <if test="areaName != null and areaName != '浙江省'">
                <choose>
                    <when test="areaName.endsWith('市')">
                        AND sys_org.city = #{areaName}
                    </when>
                    <otherwise>
                        AND sys_org.area = #{areaName}
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            law_education_resources.create_time DESC
    </select>

    <!-- 分页查询主题宣传月资源数据（带分页） -->
    <select id="lawThemeMonthPageWithPagination" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            law_education_resources.id,
            law_education_resources.title,
            law_education_resources.context_,
            law_education_resources.dept_name,
            law_education_resources.create_time,
            law_education_resources.type,
            law_education_resources.theme_type,
            law_education_resources.audience_type,
            sys_org.city,
            sys_org.area,
            CASE
                WHEN law_education_resources.type = '11' THEN '漫画'
                WHEN law_education_resources.type = '12' THEN '宣传画'
                WHEN law_education_resources.type = '13' THEN '海报'
                WHEN law_education_resources.type = '31' THEN '动画'
                WHEN law_education_resources.type = '32' THEN '宣传片'
                WHEN law_education_resources.type = '33' THEN '微电影'
                WHEN law_education_resources.type = '34' THEN '情景剧'
                WHEN law_education_resources.type = '41' THEN '视频课件'
                WHEN law_education_resources.type = '42' THEN '文档课件'
                WHEN law_education_resources.type = '1' THEN '公益广告'
                ELSE '其他'
            END as type_name
        FROM
            law_education_resources
            LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
        WHERE
            law_education_resources.status_ in (1, 2, 5, 6)
            AND law_education_resources.source_ = '0'
            AND EXISTS (
                SELECT 1 FROM law_education_resources_label_contact lc
                WHERE lc.res_id = law_education_resources.id
                AND lc.label_pid = #{labelPid}
            )
            <if test="areaName != null and areaName != '浙江省'">
                <choose>
                    <when test="areaName.endsWith('市')">
                        AND sys_org.city = #{areaName}
                    </when>
                    <otherwise>
                        AND sys_org.area = #{areaName}
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            law_education_resources.create_time DESC
    </select>

    <!-- 普法活动主题统计 -->
    <select id="lawActivityTheme" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            CASE
                WHEN theme_type IS NULL OR theme_type = '' THEN '其他'
                ELSE theme_type
            END as theme_code,
            COUNT(*) as count
        FROM law_education_resources
        WHERE source_ = '1'
          AND status_ IN (1, 2, 5, 6)
          AND dept_id LIKE 'DUTY_%'
        GROUP BY theme_type
        ORDER BY count DESC
    </select>

    <!-- 普法活动趋势统计（近12个月） -->
    <select id="lawActivityTrend" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m') as month,
            COUNT(*) as count
        FROM law_education_resources
        WHERE source_ = '1'
          AND status_ IN (1, 2, 5, 6)
          AND dept_id LIKE 'DUTY_%'
          AND create_time >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
          AND (
              YEAR(create_time) &lt; YEAR(CURDATE())
              OR (
                  YEAR(create_time) = YEAR(CURDATE())
                  AND MONTH(create_time) &lt;= MONTH(CURDATE())
              )
          )
        GROUP BY DATE_FORMAT(create_time, '%Y-%m')
        ORDER BY month ASC
    </select>

    <!-- 普法活动风采（最新10个带图文的活动） -->
    <select id="lawActivityStyle" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,
            title,
            cover,
            context_,
            dept_name,
            create_time,
            view_num,
            likes_num,
            star_num
        FROM law_education_resources
        WHERE source_ = '1'
          AND status_ IN (1, 2, 5, 6)
          AND dept_id LIKE 'DUTY_%'
          AND context_ IS NOT NULL
          AND context_ != ''
        ORDER BY create_time DESC
        LIMIT 10
    </select>

    <!-- 普法活动列表（分页） -->
    <select id="lawActivityList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,
            title,
            cover,
            context_,
            dept_name,
            create_time,
            view_num,
            likes_num,
            star_num,
            type,
            theme_type,
            audience_type
        FROM law_education_resources
        WHERE source_ = '1'
          AND status_ IN (1, 2, 5, 6)
          AND dept_id LIKE 'DUTY_%'
        ORDER BY create_time DESC
    </select>


    <select id="lawActivityTrendOrg" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            dept_id,
            dept_name,
            COUNT(*) AS num
        FROM
            law_education_resources
        WHERE
            dept_id LIKE '%DUTY_%'
            AND dept_name IS NOT NULL
        GROUP BY
            dept_id,
            dept_name
    </select>
    <select id="lawThemeMonthVideo" resultType="com.alibaba.fastjson.JSONObject">
        <choose>
            <when test="areaName == '浙江省'">
                SELECT
                sys_file_info.*
                FROM
                law_education_resources
                LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
                LEFT JOIN sys_file_info ON law_education_resources.id = sys_file_info.refer_id
                WHERE
                law_education_resources.status_ IN (1, 2, 5, 6)
                AND law_education_resources.source_ = '0'
                AND sys_org.city IS NOT NULL
                AND sys_file_info.refer_id IS NOT NULL
                AND EXISTS (SELECT 1 FROM law_education_resources_label_contact lc WHERE lc.res_id = law_education_resources.id AND lc.label_pid = #{labelPid})
                LIMIT 5
            </when>
            <when test="areaName != null and areaName.endsWith('市')">
                SELECT
                sys_file_info.*
                FROM
                law_education_resources
                LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
                LEFT JOIN sys_file_info ON law_education_resources.id = sys_file_info.refer_id
                WHERE
                law_education_resources.status_ IN (1, 2, 5, 6)
                AND law_education_resources.source_ = '0'
                AND sys_org.city = #{areaName}
                AND sys_org.area IS NOT NULL
                AND sys_file_info.refer_id IS NOT NULL
                AND EXISTS (SELECT 1 FROM law_education_resources_label_contact lc WHERE lc.res_id = law_education_resources.id AND lc.label_pid = #{labelPid})
                LIMIT 5
            </when>
            <otherwise>
                <!-- 按单位统计 -->
                SELECT
                sys_file_info.*
                FROM
                law_education_resources
                LEFT JOIN sys_org ON law_education_resources.dept_id = sys_org.id
                LEFT JOIN sys_file_info ON law_education_resources.id = sys_file_info.refer_id
                WHERE
                law_education_resources.status_ IN (1, 2, 5, 6)
                AND law_education_resources.source_ = '0'
                <if test="areaName != null and areaName != ''">
                    AND sys_org.area = #{areaName}
                </if>
                AND sys_file_info.refer_id IS NOT NULL
                AND EXISTS (SELECT 1 FROM law_education_resources_label_contact lc WHERE lc.res_id = law_education_resources.id AND lc.label_pid = #{labelPid})
                LIMIT 5
            </otherwise>
        </choose>
    </select>
</mapper>
