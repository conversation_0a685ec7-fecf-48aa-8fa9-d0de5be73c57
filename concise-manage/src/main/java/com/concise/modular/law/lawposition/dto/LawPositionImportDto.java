package com.concise.modular.law.lawposition.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 普法阵地Excel导入DTO
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
@Data
public class LawPositionImportDto {

    /**
     * 阵地名称
     */
    @Excel(name = "阵地名称", orderNum = "1")
    @ApiModelProperty(value = "阵地名称")
    private String positionName;

    /**
     * 城市
     */
    @Excel(name = "城市", orderNum = "2")
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 地区
     */
    @Excel(name = "地区", orderNum = "3")
    @ApiModelProperty(value = "地区")
    private String area;

    /**
     * 阵地级别
     */
    @Excel(name = "阵地级别", orderNum = "4")
    @ApiModelProperty(value = "阵地级别")
    private String positionLevel;

    /**
     * 阵地类型
     */
    @Excel(name = "阵地类型", orderNum = "5")
    @ApiModelProperty(value = "阵地类型")
    private String positionType;
}
