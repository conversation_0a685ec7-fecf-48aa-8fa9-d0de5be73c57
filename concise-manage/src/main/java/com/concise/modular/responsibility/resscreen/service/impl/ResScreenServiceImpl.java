package com.concise.modular.responsibility.resscreen.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.common.consts.CommonConstant;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.laweducation.entity.LeResources;
import com.concise.modular.responsibility.resexperienceresult.entity.ResExperienceResult;
import com.concise.modular.responsibility.resimplementation.entity.ResImplementation;
import com.concise.modular.responsibility.resimplementation.service.ResImplementationService;
import com.concise.modular.responsibility.resrulemanage.entity.ResRuleManage;
import com.concise.modular.responsibility.resscreen.service.ResScreenService;
import com.concise.modular.responsibility.resworkrecord.entity.ResWorkRecord;
import com.concise.modular.responsibility.resworkrecord.service.ResWorkRecordService;
import com.concise.modular.responsibility.resworksummary.entity.ResWorkSummary;
import com.concise.modular.responsibility.resworksummary.param.ResWorkSummaryParam;
import com.concise.modular.responsibility.resworksummary.service.ResWorkSummaryService;
import com.concise.modular.statistic.enums.ResourceTypeEnum;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
@Service
public class ResScreenServiceImpl implements ResScreenService {
    @Resource
    private ResWorkSummaryService resWorkSummaryService;

    @Resource
    private ResWorkRecordService resWorkRecordService;

    @Resource
    private ResImplementationService resImplementationService;

    @Resource
    private SysFileInfoService sysFileInfoService;


    @Override
    public Object checkList(String year, String type) {
        ResWorkSummaryParam resWorkSummaryParam = new ResWorkSummaryParam();
        if (ObjectUtil.isNotEmpty(year)) {
            resWorkSummaryParam.setYear(year);
        }
        if (ObjectUtil.isNotEmpty(type)) {
            resWorkSummaryParam.setOrgType(type);
        }
        PageResult<ResWorkSummary> page = resWorkSummaryService.page(resWorkSummaryParam);
        List<ResWorkSummary> collect = page.getRows().stream().peek(item -> {
            //计算个性指标数量
            int count = resWorkRecordService.count(new QueryWrapper<ResWorkRecord>().lambda()
                    .eq(ResWorkRecord::getOrgId, item.getOrgId())
                    .eq(ResWorkRecord::getYear, year));
            item.setRecordCount(count);
            if (ObjectUtil.isNotEmpty(item.getFileId())) {
                String[] split = item.getFileId().split(SymbolConstant.COMMA);
                List<SysFileInfo> fileInfos = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda()
                        .in(SysFileInfo::getId, Arrays.asList(split)));
                item.setWorkfileList(fileInfos);

            }
        }).collect(Collectors.toList());
        page.setRows(collect);
        return page;
    }

    @Override
    public Map<String, Object> responsibilityList(String year, String orgId) {
        if (ObjectUtil.isEmpty(year)) {
            year = String.valueOf(DateUtil.year(DateUtil.date()));
        }
        Map<String, Object> map = new HashMap<>();
        //普法责任制清单
        List<ResWorkRecord> list = resWorkRecordService.list(new QueryWrapper<ResWorkRecord>().lambda()
                .eq(ResWorkRecord::getOrgId, orgId)
                .eq(ResWorkRecord::getYear, year));
        map.put("list", list);
        //年度履职报告
        ResWorkSummary resWorkSummary = resWorkSummaryService.getOne(new QueryWrapper<ResWorkSummary>().lambda()
                .eq(ResWorkSummary::getOrgId, orgId)
                .eq(ResWorkSummary::getYear, year).last("limit 1"));
        if (ObjectUtil.isNotEmpty(resWorkSummary)) {
            if (ObjectUtil.isNotEmpty(resWorkSummary.getFileId())) {
                String[] split = resWorkSummary.getFileId().split(SymbolConstant.COMMA);
                List<SysFileInfo> fileInfos = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda()
                        .in(SysFileInfo::getId, Arrays.asList(split)));
                resWorkSummary.setWorkfileList(fileInfos);

            }
            map.put("resWorkSummary", resWorkSummary);
        } else {
//            ResWorkSummary resWorkSummary1 = new ResWorkSummary();
//            resWorkSummary1.setYear(year);
//            resWorkSummary1.setOrgId(orgId);
//            resWorkSummary1.setOrgName(resWorkRecordService.getById(orgId).getOrgName());
            map.put("resWorkSummary", null);
        }
        return map;
    }

    @Override
    public Map<String, Object> responsibilityDetail(String year, String orgId) {
        if (ObjectUtil.isEmpty(year)) {
            year = String.valueOf(DateUtil.year(DateUtil.date()));
        }
        //初始化本年度普法责任制落实情况
        ResImplementation resImplementation = resImplementationService.getOne(new QueryWrapper<ResImplementation>().lambda().eq(ResImplementation::getYear, year).eq(ResImplementation::getOrgId, orgId));
        //计算当前排名（与 responsibilityRank 接口保持一致）
        calculateCurrentRanking(resImplementation, year);
//        resImplementationService.calculateRank(resImplementation);
        Map<String, Object> map = new HashMap<>();

        map.put("info", resImplementation);
        List<ResRuleManage> resRuleManages = resImplementationService.myTask(resImplementation, year);

        List<ResWorkRecord> recordList = new ArrayList<>();
        List<ResExperienceResult> resExperienceResultList = new ArrayList<>();
        List<LeResources> leResourcesList = new ArrayList<>();
        for (ResRuleManage resRuleManage : resRuleManages) {
            //只展示已打分
            if ("个性指标".equals(resRuleManage.getRuleType())) {
                recordList = resRuleManage.getResWorkRecordList();
                if (CollectionUtil.isNotEmpty(recordList)) {
                    recordList.forEach(record -> {
                        if (ObjectUtil.isNotEmpty(record.getFileId())) {
                            String[] split = record.getFileId().split(SymbolConstant.COMMA);
                            List<SysFileInfo> fileInfos = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda()
                                    .in(SysFileInfo::getId, Arrays.asList(split)));
                            record.setFileList(fileInfos);
                        }
                    });
                }
            }
            if ("经验成果".equals(resRuleManage.getRuleType())) {
                resExperienceResultList = resRuleManage.getResExperienceResultList();
                if (CollectionUtil.isNotEmpty(resExperienceResultList)) {
                    resExperienceResultList.forEach(item -> {
                        if (ObjectUtil.isNotEmpty(item.getFileIds())) {
                            String[] split = item.getFileIds().split(SymbolConstant.COMMA);
                            List<SysFileInfo> fileInfos = sysFileInfoService.list(new QueryWrapper<SysFileInfo>().lambda()
                                    .in(SysFileInfo::getId, Arrays.asList(split)));
                            item.setFileList(fileInfos);
                        }
                    });
                }
            }
            if ("原创普法资源".equals(resRuleManage.getRuleType())) {
                leResourcesList = resRuleManage.getLeResourcesList();

            }
        }

        //个性指标
        map.put("recordList", recordList);
        //经验成果
        map.put("resExperienceResultList", resExperienceResultList);
        //原创普法资源,分类统计数量
        Map<String, Long> collect = leResourcesList.stream().collect(Collectors.groupingBy(LeResources::getType, Collectors.counting()));

        //创建返回结果数组
        List<Map<String, Object>> resourceTypeList = new ArrayList<>();

        //确保所有枚举类型都有值，没有数据时返回0
        for (ResourceTypeEnum type : ResourceTypeEnum.values()) {
            Map<String, Object> typeMap = new HashMap<>();
            typeMap.put("name", type.getName());
            typeMap.put("value", 0L);
            resourceTypeList.add(typeMap);
        }

        //如果有数据则覆盖默认值
        collect.forEach((key, value) -> {
            ResourceTypeEnum typeEnum = ResourceTypeEnum.getByCode(Integer.parseInt(key.substring(0, 1)));
            if (typeEnum != null) {
                //查找对应类型的项并更新数值
                resourceTypeList.stream()
                        .filter(item -> typeEnum.getName().equals(item.get("name")))
                        .findFirst()
                        .ifPresent(item -> item.put("value", value));
            }
        });

        map.put("leResourcesList", resourceTypeList);
        return map;
    }



    @Override
    public List<ResImplementation> responsibilityRank(String year) {
        final String finalYear;
        if (ObjectUtil.isEmpty(year)) {
            finalYear = String.valueOf(DateUtil.year(DateUtil.date()));
        } else {
            finalYear = year;
        }
        List<ResImplementation> list = resImplementationService.list(new QueryWrapper<ResImplementation>().lambda().eq(ResImplementation::getSorts, CommonConstant.YES).eq(ResImplementation::getYear, finalYear).orderByDesc(ResImplementation::getScore).last("limit 100"));
        //添加排名
        for (int i = 0; i < list.size(); i++) {
            ResImplementation item = list.get(i);
            // 直接设置排名为索引+1，确保从1开始
            item.setCurrentRanking(String.valueOf(i + 1));
        }
        return list;
    }

    /**
     * 统一的排名计算方法
     * 确保与 responsibilityRank 接口的排名逻辑完全一致
     */
    private void calculateCurrentRanking(ResImplementation resImplementation, String year) {
        // 使用与 responsibilityRank 相同的查询条件：sorts='Y' AND year=指定年份
        List<ResImplementation> allRankingList = resImplementationService.list(
                new QueryWrapper<ResImplementation>().lambda()
                        .eq(ResImplementation::getSorts, CommonConstant.YES)
                        .eq(ResImplementation::getYear, year)
                        .orderByDesc(ResImplementation::getScore)
        );

        if (allRankingList.isEmpty()) {
            resImplementation.setCurrentRanking("0");
            return;
        }

        // 查找当前单位在排名列表中的位置
        for (int i = 0; i < allRankingList.size(); i++) {
            if (allRankingList.get(i).getOrgId().equals(resImplementation.getOrgId())) {
                resImplementation.setCurrentRanking(String.valueOf(i + 1));
                return;
            }
        }

        // 如果当前单位不参与排名（sorts != 'Y'），则显示为未排名
        resImplementation.setCurrentRanking("未参与排名");
    }
}
